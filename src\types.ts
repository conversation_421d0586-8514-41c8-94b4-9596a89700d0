export interface Customer {
  id: string;
  name: string;
  phone: string;
  email: string;
  address: string;
  notes: string;
  status: 'active' | 'inactive';
  totalOrders: number;
  totalSpent: number;
  lastVisit: string;
  loyaltyPoints: number;
}

// Standardized status types for operations and tickets
export type OperationStatus = 'in_progress' | 'pending' | 'ready' | 'completed' | 'held' | 'cancelled';
export type TicketStatus = 'pending' | 'ready' | 'completed';

// Helper function to convert operation status to ticket status
export const mapOperationStatusToTicketStatus = (status: OperationStatus): TicketStatus => {
  if (status === 'ready') return 'ready';
  if (status === 'completed') return 'completed';
  return 'pending'; // Default for in_progress, held, cancelled, and pending
};
