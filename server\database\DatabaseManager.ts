import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { TenantDatabaseManager } from './TenantDatabaseManager';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class DatabaseManager {
  private static instance: DatabaseManager;
  private db: Database.Database;
  private dbPath: string;
  private tenantDbManager: TenantDatabaseManager;

  private constructor() {
    // Initialize tenant database manager
    this.tenantDbManager = TenantDatabaseManager.getInstance();

    // Use environment variable or default path for default tenant
    this.dbPath = process.env.DATABASE_PATH || path.join(__dirname, '..', 'shoe_repair_pos.db');
    this.initializeDatabase();
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  private initializeDatabase(): void {
    try {
      // Create database connection
      this.db = new Database(this.dbPath);
      
      // Enable foreign keys and other pragmas
      this.db.pragma('foreign_keys = ON');
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      
      // Load and execute unified schema
      this.loadSchema();
      
      // Initialize with default data if needed
      this.initializeDefaultData();
      
      console.log(`✅ Database initialized successfully at: ${this.dbPath}`);
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      throw error;
    }
  }

  private loadSchema(): void {
    try {
      const schemaPath = path.join(__dirname, 'unified_schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      this.db.exec(schema);
      console.log('✅ Database schema loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load database schema:', error);
      throw error;
    }
  }

  private initializeDefaultData(): void {
    try {
      // Check if we need to initialize default data
      const categoriesCount = this.db.prepare('SELECT COUNT(*) as count FROM categories').get() as { count: number };
      
      if (categoriesCount.count === 0) {
        this.insertDefaultCategories();
      }

      const servicesCount = this.db.prepare('SELECT COUNT(*) as count FROM services').get() as { count: number };
      
      if (servicesCount.count === 0) {
        this.insertDefaultServices();
      }

      console.log('✅ Default data initialized');
    } catch (error) {
      console.error('❌ Failed to initialize default data:', error);
      // Don't throw here, as this is not critical
    }
  }

  private insertDefaultCategories(): void {
    const defaultCategories = [
      { id: 'cat_polish', name: 'Polish', description: 'Shoe polish products' },
      { id: 'cat_laces', name: 'Laces', description: 'Shoe laces' },
      { id: 'cat_insoles', name: 'Insoles', description: 'Shoe insoles' },
      { id: 'cat_accessories', name: 'Accessories', description: 'Other shoe accessories' },
      { id: 'cat_repair', name: 'Repair Services', description: 'Shoe repair services' },
      { id: 'cat_cleaning', name: 'Cleaning', description: 'Shoe cleaning services' }
    ];

    const insertCategory = this.db.prepare(`
      INSERT INTO categories (id, name, description, created_at, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);

    const transaction = this.db.transaction(() => {
      defaultCategories.forEach(category => {
        insertCategory.run(category.id, category.name, category.description);
      });
    });

    transaction();
    console.log('✅ Default categories inserted');
  }

  private insertDefaultServices(): void {
    const defaultServices = [
      { id: 'srv_basic_repair', name: 'Basic Repair', price: 25.00, estimated_days: 3, category: 'repair' },
      { id: 'srv_polish', name: 'Polish Service', price: 15.00, estimated_days: 1, category: 'polish' },
      { id: 'srv_deep_clean', name: 'Deep Cleaning', price: 20.00, estimated_days: 2, category: 'cleaning' },
      { id: 'srv_sole_repair', name: 'Sole Repair', price: 35.00, estimated_days: 5, category: 'repair' },
      { id: 'srv_heel_repair', name: 'Heel Repair', price: 30.00, estimated_days: 4, category: 'repair' },
      { id: 'srv_lace_replacement', name: 'Lace Replacement', price: 10.00, estimated_days: 1, category: 'accessories' }
    ];

    const insertService = this.db.prepare(`
      INSERT INTO services (id, name, description, price, estimated_days, category, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);

    const transaction = this.db.transaction(() => {
      defaultServices.forEach(service => {
        insertService.run(
          service.id, 
          service.name, 
          service.name, // description same as name for now
          service.price, 
          service.estimated_days, 
          service.category
        );
      });
    });

    transaction();
    console.log('✅ Default services inserted');
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  // Get tenant-specific database
  public async getTenantDatabase(tenantId: string): Promise<Database.Database> {
    return await this.tenantDbManager.getTenantDatabase(tenantId);
  }

  // Get master database for tenant management
  public getMasterDatabase(): Database.Database {
    return this.tenantDbManager.getMasterDatabase();
  }

  // Get tenant database manager
  public getTenantDatabaseManager(): TenantDatabaseManager {
    return this.tenantDbManager;
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      console.log('✅ Database connection closed');
    }

    // Close tenant database connections
    this.tenantDbManager.closeAllConnections();
  }

  // Health check method
  public healthCheck(): boolean {
    try {
      const result = this.db.prepare('SELECT 1 as test').get();
      return result && (result as any).test === 1;
    } catch (error) {
      console.error('❌ Database health check failed:', error);
      return false;
    }
  }

  // Get database statistics
  public getStats(): any {
    try {
      const stats = {
        customers: this.db.prepare('SELECT COUNT(*) as count FROM customers').get(),
        operations: this.db.prepare('SELECT COUNT(*) as count FROM operations').get(),
        services: this.db.prepare('SELECT COUNT(*) as count FROM services').get(),
        categories: this.db.prepare('SELECT COUNT(*) as count FROM categories').get(),
        products: this.db.prepare('SELECT COUNT(*) as count FROM products').get(),
        hold_items: this.db.prepare('SELECT COUNT(*) as count FROM hold_items').get()
      };
      return stats;
    } catch (error) {
      console.error('❌ Failed to get database stats:', error);
      return null;
    }
  }
}

// Export singleton instance
const dbManager = DatabaseManager.getInstance();
export const db = dbManager.getDatabase();
export default db;
