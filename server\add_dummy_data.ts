import db from './database';
import crypto from 'crypto';

const dummyCustomers = [
    ['<PERSON>', '555-0101', '<EMAIL>', '123 Main St, Anytown, USA', 'Regular customer'],
    ['<PERSON>', '555-0102', '<EMAIL>', '456 Oak Ave, Springfield, USA', 'Prefers evening appointments'],
    ['<PERSON>', '555-0103', '<EMAIL>', '789 Pine Rd, Riverside, USA', 'Has loyalty card'],
    ['<PERSON>', '555-0104', 'e<PERSON><PERSON>@email.com', '321 Elm St, Lakeside, USA', 'Weekly customer'],
    ['<PERSON>', '555-0105', '<EMAIL>', '654 Maple Dr, Highland, USA', ''],
    ['<PERSON>', '555-0106', '<EMAIL>', '987 Cedar Ln, Westville, USA', 'Special care for leather shoes'],
    ['<PERSON>', '555-0107', 'r<PERSON><PERSON><EMAIL>', '147 Birch St, Easttown, USA', ''],
    ['<PERSON>', '555-0108', '<EMAIL>', '258 Walnut Ave, Southend, USA', 'Premium member'],
    ['<PERSON>', '555-0109', '<EMAIL>', '369 Cherry Rd, Northside, USA', ''],
    ['Jennifer Garcia', '555-0110', '<EMAIL>', '741 Ash St, Central City, USA', 'Allergic to certain polishes'],
    ['<PERSON>', '555-0111', '<EMAIL>', '852 Spruce Dr, Midtown, USA', ''],
    ['Elizabeth Lee', '555-0112', '<EMAIL>', '963 Fir Ave, Downtown, USA', 'Business account'],
    ['Thomas Moore', '555-0113', '<EMAIL>', '159 Pine St, Uptown, USA', ''],
    ['Mary White', '555-0114', '<EMAIL>', '357 Oak Rd, Westside, USA', 'Monthly regular'],
    ['Charles King', '555-0115', '<EMAIL>', '486 Maple Ave, Eastside, USA', ''],
    ['Susan Wright', '555-0116', '<EMAIL>', '753 Cedar St, Northtown, USA', 'Prefers eco-friendly products'],
    ['Joseph Lopez', '555-0117', '<EMAIL>', '951 Elm Dr, Southtown, USA', ''],
    ['Margaret Hall', '555-0118', '<EMAIL>', '147 Birch Ave, Westend, USA', 'VIP customer'],
    ['Richard Adams', '555-0119', '<EMAIL>', '258 Walnut St, Eastend, USA', ''],
    ['Barbara Clark', '555-0120', '<EMAIL>', '369 Cherry Ln, Downtown, USA', 'Frequent buyer']
];

async function addDummyCustomers() {
    for (const customer of dummyCustomers) {
        try {
            const stmt = db.prepare('INSERT INTO customers (id, name, phone, email, address, notes) VALUES (?, ?, ?, ?, ?, ?)');
            const id = crypto.randomUUID();
            stmt.run(id, ...customer);
            console.log(`Added customer: ${customer[0]}`);
        } catch (error) {
            console.error(`Error adding customer ${customer[0]}:`, error);
        }
    }
    console.log('Finished adding dummy customers');
    process.exit(0);
}

addDummyCustomers();
