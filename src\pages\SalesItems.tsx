import React, { useState } from 'react';
import { ShoppingCart, Minus, Plus, X, Search, Tag, DollarSign, Trash2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import AddProductModal from '../components/AddProductModal';
import { useProducts } from '../contexts/ProductContext';
import toast from 'react-hot-toast';
import './SalesItems.css';

interface CartItem {
  item: {
    id: string;
    name: string;
    price: number;
    categoryId: string;
    imageUrl?: string;
  };
  quantity: number;
}

interface CategoryUsage {
  [key: string]: number;
}

const SalesItems: React.FC = () => {
  const { products, categories } = useProducts();
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [isAddProductModalOpen, setIsAddProductModalOpen] = useState(false);
  const [subtotal, setSubtotal] = useState(0);
  const [tax, setTax] = useState(0);
  const [total, setTotal] = useState(0);
  const [discountOrUpcharge, setDiscountOrUpcharge] = useState(0);
  const [showCart, setShowCart] = useState(false);
  const [isTaxable, setIsTaxable] = useState(false);
  const [isReceipt, setIsReceipt] = useState(false);
  const [deleteCategoryId, setDeleteCategoryId] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [categoryUsage, setCategoryUsage] = useState<CategoryUsage>({});

  const handleAddToCart = (item: any) => {
    setCart(prevCart => {
      const existingItem = prevCart.find(cartItem => cartItem.item.id === item.id);
      if (existingItem) {
        return prevCart.map(cartItem =>
          cartItem.item.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      }
      return [...prevCart, { item, quantity: 1 }];
    });
    updateTotals();
  };

  const updateTotals = () => {
    const newSubtotal = cart.reduce((sum, item) => sum + (item.quantity * item.item.price), 0);
    const newTax = isTaxable ? newSubtotal * 0.07 : 0; // 7% tax rate
    setSubtotal(newSubtotal);
    setTax(newTax);
    setTotal(newSubtotal + newTax + discountOrUpcharge);
  };

  const filteredItems = selectedCategory
    ? products.filter(item => item.categoryId === selectedCategory)
    : products;

  // This function is used in the UI to show the delete confirmation modal
  const handleShowDeleteModal = (categoryId: string) => {
    setDeleteCategoryId(categoryId);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDeleteCategory = () => {
    if (deleteCategoryId) {
      const categoryUsageCount = categoryUsage[deleteCategoryId] || 0;
      if (categoryUsageCount > 0) {
        alert(`Cannot delete category with ${categoryUsageCount} items assigned`);
      } else {
        // Implement delete category logic
        setIsDeleteModalOpen(false);
        setDeleteCategoryId(null);
      }
    }
  };

  const handleTaxableClick = () => {
    setIsTaxable(!isTaxable);
    updateTotals();
  };

  const handleReceiptClick = () => {
    setIsReceipt(!isReceipt);
  };

  const updateCategoryUsage = () => {
    const usage: CategoryUsage = {};
    products.forEach(item => {
      if (usage[item.categoryId]) {
        usage[item.categoryId]++;
      } else {
        usage[item.categoryId] = 1;
      }
    });
    setCategoryUsage(usage);
  };

  React.useEffect(() => {
    updateCategoryUsage();
  }, [products]);

  const handleRemoveFromCart = (index: number) => {
    setCart(prevCart => prevCart.filter((_, i) => i !== index));
    updateTotals();
    toast.success('Item removed from cart');
  };

  const handleQuantityChange = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setCart(prevCart =>
      prevCart.map((cartItem, i) =>
        i === index
          ? { ...cartItem, quantity: newQuantity }
          : cartItem
      )
    );
    updateTotals();
  };

  const handleDiscountChange = (value: number) => {
    setDiscountOrUpcharge(value);
    updateTotals();
  };

  const handleCompletePurchase = async () => {
    if (cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }

    try {
      // Here you would typically:
      // 1. Send the order to your backend
      // 2. Update inventory
      // 3. Generate receipt
      // 4. Process payment
      
      // For now, we'll just show a success message and clear the cart
      const orderSummary = {
        items: cart,
        subtotal,
        tax,
        discountOrUpcharge,
        total,
        timestamp: new Date().toISOString(),
        isReceipt,
        isTaxable
      };

      console.log('Order completed:', orderSummary);
      
      // Clear the cart
      setCart([]);
      setSubtotal(0);
      setTax(0);
      setTotal(0);
      setDiscountOrUpcharge(0);
      setShowCart(false);
      
      toast.success('Purchase completed successfully!');
    } catch (error) {
      toast.error('Failed to complete purchase. Please try again.');
      console.error('Purchase error:', error);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-900 via-indigo-900 to-gray-900 p-4 flex items-center justify-between shadow-lg">
        <div className="flex-1 flex items-center space-x-4">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-xl">
            <input
              type="text"
              placeholder="Search products..."
              className="w-full bg-gradient-to-r from-gray-800 to-gray-700 text-white pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 border border-gray-600 hover:border-indigo-500 transition-all duration-300"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400" size={20} />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4">
          {/* Manage Categories */}
          <Link
            to="/manage-categories"
            className="flex items-center space-x-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg"
          >
            <Tag size={18} />
            <span>Manage Categories</span>
          </Link>

          {/* Discount Button */}
          <button
            onClick={() => setShowCart(true)}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg"
          >
            <DollarSign size={18} />
            <span>Discount</span>
          </button>

          {/* Cart Button */}
          <button
            onClick={() => setShowCart(true)}
            className="relative flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-4 py-2 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg"
          >
            <ShoppingCart size={18} />
            <span>Cart ({cart.length})</span>
          </button>
        </div>
      </div>

      {/* Totals */}
      <div className="bg-gray-900 p-4 border-b border-gray-800">
        <div className="flex justify-between items-center">
          <div className="flex space-x-8">
            <div>
              <span className="text-gray-400">Subtotal</span>
              <div className="text-white">${subtotal.toFixed(2)}</div>
            </div>
            <div>
              <span className="text-gray-400">Discount/Upcharge</span>
              <div className="text-white">${discountOrUpcharge.toFixed(2)}</div>
            </div>
            <div>
              <span className="text-gray-400">Tax</span>
              <div className="text-white">${tax.toFixed(2)}</div>
            </div>
            <div>
              <span className="text-gray-400">Total</span>
              <div className="text-green-500 font-bold">${total.toFixed(2)}</div>
            </div>
          </div>
          <div className="flex space-x-2">
            <button 
              onClick={handleTaxableClick}
              className={`bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 ${isTaxable ? 'bg-green-500' : ''}`}
            >
              <span>Taxable</span>
            </button>
            <button 
              onClick={handleReceiptClick}
              className={`bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 ${isReceipt ? 'bg-green-500' : ''}`}
            >
              <span>Receipt</span>
            </button>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="p-6 flex gap-3 overflow-x-auto hide-scrollbar bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900">
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`
              px-6 py-3 
              rounded-full 
              font-medium 
              transform transition-all duration-300
              shadow-lg
              flex items-center gap-2
              min-w-fit
              ${
                selectedCategory === category.id
                  ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white scale-105 shadow-blue-500/50'
                  : 'bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300 hover:from-gray-600 hover:to-gray-800 hover:text-white hover:scale-105'
              }
            `}
          >
            <Tag size={16} className="opacity-70" />
            <span>{category.name}</span>
          </button>
        ))}
      </div>

      {/* Products Grid */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="grid grid-cols-5 gap-6">
          {filteredItems.map(item => (
            <button
              key={item.id}
              onClick={() => handleAddToCart(item)}
              className="bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl overflow-hidden hover:ring-2 hover:ring-blue-500 hover:transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <div className="aspect-square bg-gray-700 relative overflow-hidden">
                <img
                  src={item.imageUrl || '/placeholder.png'}
                  alt={item.name}
                  className="w-full h-full object-cover transform hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
                  <p className="text-green-400 font-bold text-xl">
                    ${item.price.toFixed(2)}
                  </p>
                </div>
              </div>
              <div className="p-4 text-left">
                <h3 className="text-white font-medium text-lg mb-1 line-clamp-2">{item.name}</h3>
                <p className="text-gray-400 text-sm">Click to add to cart</p>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Cart Sidebar */}
      <div 
        className={`fixed right-0 top-0 h-screen w-96 bg-gray-900 shadow-2xl transform transition-transform duration-300 ${
          showCart ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="h-full flex flex-col">
          <div className="p-4 border-b border-gray-700 flex justify-between items-center bg-gradient-to-r from-gray-800 to-gray-900">
            <h2 className="text-xl font-bold text-white">Shopping Cart</h2>
            <button onClick={() => setShowCart(false)} className="text-gray-400 hover:text-white">
              <X size={24} />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-auto p-4">
            {cart.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <ShoppingCart size={48} className="mb-4 opacity-50" />
                <p className="text-lg">Your cart is empty</p>
              </div>
            ) : (
              cart.map((item, index) => (
                <div key={index} className="flex justify-between items-center p-4 mb-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-4">
                    <img
                      src={item.item.imageUrl || '/placeholder.png'}
                      alt={item.item.name}
                      className="w-16 h-16 object-cover rounded-lg"
                    />
                    <div>
                      <div className="font-medium text-white">{item.item.name}</div>
                      <div className="text-sm text-gray-400">
                        ${item.item.price.toFixed(2)} x {item.quantity}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => handleQuantityChange(index, item.quantity - 1)}
                      className="p-1 text-gray-400 hover:text-white transition-colors"
                    >
                      <Minus size={16} />
                    </button>
                    <span className="w-8 text-center font-medium text-white">{item.quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(index, item.quantity + 1)}
                      className="p-1 text-gray-400 hover:text-white transition-colors"
                    >
                      <Plus size={16} />
                    </button>
                    <button
                      onClick={() => handleRemoveFromCart(index)}
                      className="p-1 text-red-400 hover:text-red-300 transition-colors ml-2"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Cart Footer */}
          <div className="p-4 border-t border-gray-700 bg-gray-800">
            {/* Discount/Upcharge Input */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Discount (-) or Upcharge (+)
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <DollarSign size={16} />
                </span>
                <input
                  type="number"
                  value={discountOrUpcharge}
                  onChange={(e) => handleDiscountChange(parseFloat(e.target.value) || 0)}
                  className="w-full bg-gray-700 text-white pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  placeholder="0.00"
                  step="0.01"
                />
              </div>
            </div>

            {/* Totals */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-gray-300">
                <span>Subtotal:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-300">
                <span>Discount/Upcharge:</span>
                <span className={discountOrUpcharge >= 0 ? 'text-green-400' : 'text-red-400'}>
                  ${discountOrUpcharge.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between text-gray-300">
                <span>Tax:</span>
                <span>${tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-white font-bold text-lg">
                <span>Total:</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>

            {/* Complete Purchase Button */}
            <button
              onClick={handleCompletePurchase}
              disabled={cart.length === 0}
              className="w-full bg-gradient-to-r from-green-600 to-green-500 text-white py-3 rounded-lg hover:from-green-700 hover:to-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
            >
              <ShoppingCart size={20} />
              <span>Complete Purchase (${total.toFixed(2)})</span>
            </button>
          </div>
        </div>
      </div>

      {/* Delete Category Modal */}
      {isDeleteModalOpen && (
        <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-4 w-96">
            <h2 className="text-lg font-bold mb-2">Confirm Delete Category</h2>
            <p className="text-gray-600 mb-4">Are you sure you want to delete this category?</p>
            {deleteCategoryId && categoryUsage[deleteCategoryId] > 0 && (
              <p className="text-red-500 mb-4">
                Warning: This category has {deleteCategoryId && categoryUsage[deleteCategoryId]} items assigned. Deleting it will remove these items from the catalog.
              </p>
            )}
            <div className="flex justify-between">
              <button 
                onClick={() => setIsDeleteModalOpen(false)}
                className="bg-gray-200 text-gray-600 py-2 px-4 rounded-lg hover:bg-gray-300"
              >
                Cancel
              </button>
              <button 
                onClick={handleConfirmDeleteCategory}
                className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Product Modal */}
      <AddProductModal
        isOpen={isAddProductModalOpen}
        onClose={() => setIsAddProductModalOpen(false)}
      />
    </div>
  );
};

export default SalesItems;
