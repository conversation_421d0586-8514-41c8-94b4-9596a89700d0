{"extensionDiagnostics": {"url": "vscode-webview://1ro8crmf7af2o4i3ou4vje5ulf1q6iad42juuqh3u6o475ol6qmp/index.html?id=5ebd4142-05ea-4169-8a19-f14eb842103e&origin=0e36b6b3-7a46-438b-898a-45c301f1525d&swVersion=4&extensionId=codeium.windsurf&platform=electron&vscode-resource-base-authority=vscode-resource.vscode-cdn.net&parentOrigin=vscode-file%3A%2F%2Fvscode-app&purpose=webviewView", "connection": {"status": "DISCONNECTED", "error": {}}, "error": {"name": "ConnectError", "message": "[unavailable] unavailable: dial tcp: lookup server.codeium.com: no such host", "stack": ["ConnectError: [unavailable] unavailable: dial tcp: lookup server.codeium.com: no such host", "    at br (https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/out/media/chat.js:2:5419660)", "    at next (https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/out/media/chat.js:2:5425085)", "    at async Object.unary (https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/out/media/chat.js:2:5424256)", "    at async Object.getUserStatus (https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/out/media/chat.js:2:5414691)", "    at async Promise.all (index 0)", "    at async https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/AppData/Local/Programs/Windsurf/resources/app/extensions/windsurf/out/media/chat.js:2:6056611"]}, "isChatEnabled": true, "handshake": {}, "params": {"ideTelemetryEnabled": true, "ready": true, "defaultView": "cascade", "apiKey": "13a196f6-2f90-4deb-b00e-c32e23917104", "extensionName": "windsurf", "extensionVersion": "1.28.3", "ideName": "windsurf", "ideVersion": "Windsurf 1.94.0", "locale": "en", "hasDevExtension": false, "appName": "windsurf", "hasEnterpriseExtension": false, "hasIndexService": false, "diffViewEnabled": true, "openFilePointerEnabled": true, "insertAtCursorEnabled": true, "cascadeInitId": ""}, "data": {"numMessages": 0}, "bridge": {"bridgeType": "null"}, "health": {"checkStatus": null, "error": {"name": "TypeError", "message": "Cannot read properties of undefined (reading 'replace')"}}}, "extensionPanelLogs": ["[WARN] 2024-12-1 17:56:2.58 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.58 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.59 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.59 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.60 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.60 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.60 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.60 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.60 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.60 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.61 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.61 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.61 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.61 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.61 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.61 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.62 [react-tooltip] Element with id 'react-tooltip-core-styles' already exists. Call `removeStyle()` first", "[WARN] 2024-12-1 17:56:2.62 [react-tooltip] Element with id 'react-tooltip-base-styles' already exists. Call `removeStyle()` first"], "languageServerDiagnostics": {"logs": ["I1201 17:55:46.754555 12668 main.go:567] Setting GOMAXPROCS to 8\n", "I1201 17:55:46.754555 12668 main.go:779] Starting language server process with pid 12668\n", "I1201 17:55:46.754555 12668 server.go:203] Language server will attempt to listen on host 127.0.0.1\n", "I1201 17:55:46.755556 12668 server.go:210] Language server listening on random port at 49792\n", "I1201 17:55:46.755556 12668 unleash.go:94] Initializing Unleash\n", "I1201 17:55:46.785034 12668 unleash.go:114] Successfully initialized Unleash\n", "E1201 17:55:46.785034 12668 unleash.go:64] Unleash error: Get \"https://unleash.codeium.com/api/client/features\": dial tcp: lookup unleash.codeium.com: no such host\n", "E1201 17:55:46.785562 12668 unleash.go:64] Unleash error: Post \"https://unleash.codeium.com/api/client/register\": dial tcp: lookup unleash.codeium.com: no such host\n", "I1201 17:55:46.785562 12668 server.go:271] Created extension server client at port 49787\n", "E1201 17:55:47.264172 12668 client_manager.go:275] <PERSON><PERSON><PERSON> is nil when trying to refresh user JWT\n", "I1201 17:55:47.266177 12668 server.go:494] Successfully created API server client\n", "E1201 17:55:47.267174 12668 api_server_client.go:309] GetUnleashContextFields error: dial tcp: lookup server.codeium.com: no such host\n", "I1201 17:55:47.325377 12668 server.go:509] Successfully initialized tokenizer\n", "W1201 17:55:48.473505 12668 interceptor.go:101] request metadata is nil\n", "W1201 17:55:48.473505 12668 interceptor.go:101] request metadata is nil\n", "I1201 17:55:50.203302 12668 server.go:675] Local search is enabled, will index local files.\n", "I1201 17:55:50.203302 12668 server.go:679] Using 2 indexer workers\n", "I1201 17:55:51.802071 12668 sqlite_faiss_db_client.go:64] Successfully created embedding search database in 1597ms\n", "I1201 17:55:51.802603 12668 indexer.go:213] Using 8 embed workers\n", "I1201 17:55:51.802603 12668 search_provider.go:274] Successfully created and started indexer\n", "I1201 17:55:51.803121 12668 search_provider.go:304] Successfully created embedding search provider\n", "I1201 17:55:51.807469 12668 server.go:766] Successfully created completion provider\n", "I1201 17:55:51.852569 12668 server.go:327] LSP listening on random port at 49796\n", "E1201 17:55:51.855182 12668 api_server_client.go:220] Ping error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:51.855182 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "I1201 17:55:51.922415 12668 utils.go:227] Analyzing workspace files for c:/Users/<USER>/Desktop/Shoe Repair POS\n", "I1201 17:55:52.117722 12668 utils.go:281] Done analyzing workspace files for c:/Users/<USER>/Desktop/Shoe Repair POS\n", "E1201 17:55:52.857300 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "I1201 17:55:52.925747 12668 lazy_model.go:31] Successfully created embedding store\n", "E1201 17:55:53.858493 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:54.858738 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:55.279397 12668 api_server_client.go:251] GetUserStatus error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:55.858858 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:56.864951 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:56.972592 12668 embedding.go:41] GetEmbeddings error: dial tcp: lookup inference.codeium.com: no such host\n", "E1201 17:55:56.972592 12668 embedding.go:41] GetEmbeddings error: dial tcp: lookup inference.codeium.com: no such host\n", "E1201 17:55:56.972592 12668 embedding.go:41] GetEmbeddings error: dial tcp: lookup inference.codeium.com: no such host\n", "E1201 17:55:57.865911 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:58.866925 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:55:59.867628 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:00.868715 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:01.871912 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:02.051472 12668 api_server_client.go:251] GetUserStatus error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:02.055611 12668 api_server_client.go:251] GetUserStatus error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:02.872883 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:03.873675 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:04.342294 12668 client_manager.go:294] Error getting user JWT: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:04.874437 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:05.875356 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:06.569890 12668 telemetry.go:135] Error traces error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:06.879183 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:07.880065 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:08.880176 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:09.880340 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:10.881076 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:11.883235 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:12.884279 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:13.884519 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:14.885132 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:15.885465 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:16.888406 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:17.889140 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:18.889399 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:19.891051 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:20.891575 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:21.855763 12668 api_server_client.go:220] Ping error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:21.900909 12668 api_server_client.go:297] GetConfig error: dial tcp: lookup server.codeium.com: no such host\n", "E1201 17:56:25.955354 12668 api_server_client.go:297] GetConfig error: read tcp *************:49872 -> **************:443: wsarecv: An established connection was aborted by the software in your host machine.\n"]}}