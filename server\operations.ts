import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import db from './database';
import { transformOperation } from './utils';

const router = express.Router();

// Create operation table if it doesn't exist
db.exec(`
  CREATE TABLE IF NOT EXISTS operations (
    id TEXT PRIMARY KEY,
    customer_id TEXT,
    status TEXT DEFAULT 'pending',
    total_amount REAL NOT NULL DEFAULT 0,
    paid_amount REAL DEFAULT 0,
    notes TEXT,
    promised_date TEXT,
    created_at TEXT,
    updated_at TEXT,
    is_no_charge INTEGER DEFAULT 0,
    is_do_over INTEGER DEFAULT 0,
    is_delivery INTEGER DEFAULT 0,
    is_pickup INTEGER DEFAULT 0,
    FOREIGN KEY (customer_id) REFERENCES customers (id)
  )
`);

// Create operation_shoes table for the many-to-many relationship
db.exec(`
  CREATE TABLE IF NOT EXISTS operation_shoes (
    id TEXT PRIMARY KEY,
    operation_id TEXT NOT NULL,
    category TEXT NOT NULL,
    color TEXT,
    notes TEXT,
    created_at TEXT,
    updated_at TEXT,
    FOREIGN KEY (operation_id) REFERENCES operations (id)
  )
`);

// Create operation_services table for the many-to-many relationship
db.exec(`
  CREATE TABLE IF NOT EXISTS operation_services (
    id TEXT PRIMARY KEY,
    operation_shoe_id TEXT NOT NULL,
    service_id TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    price REAL NOT NULL,
    notes TEXT,
    created_at TEXT,
    updated_at TEXT,
    FOREIGN KEY (operation_shoe_id) REFERENCES operation_shoes (id),
    FOREIGN KEY (service_id) REFERENCES services (id)
  )
`);

// Get all operations
router.get('/', (req, res) => {
  try {
    const operations = db.prepare(`
      SELECT o.*, c.name as customer_name, c.phone as customer_phone
      FROM operations o
      LEFT JOIN customers c ON o.customer_id = c.id
      ORDER BY o.created_at DESC
    `).all();

    // Get shoes and services for each operation
    const operationsWithShoes = operations.map(operation => {
      const shoes = db.prepare(`
        SELECT os.*, s.name as service_name, s.price as service_base_price
        FROM operation_shoes os
        LEFT JOIN operation_services oss ON os.id = oss.operation_shoe_id
        LEFT JOIN services s ON oss.service_id = s.id
        WHERE os.operation_id = ?
      `).all(operation.id);

      return {
        ...operation,
        shoes: shoes.map(shoe => ({
          id: shoe.id,
          category: shoe.category,
          color: shoe.color,
          notes: shoe.notes,
          services: [{
            id: shoe.service_id,
            name: shoe.service_name,
            price: shoe.price,
            basePrice: shoe.service_base_price
          }]
        }))
      };
    });

    res.json(operationsWithShoes.map(transformOperation));
  } catch (error) {
    console.error('Failed to fetch operations:', error);
    res.status(500).json({ error: 'Failed to fetch operations' });
  }
});

// Get operation by ID
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const operation = db.prepare(`
      SELECT o.*, c.name as customer_name, c.phone as customer_phone
      FROM operations o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.id = ?
    `).get(id);
    
    if (!operation) {
      return res.status(404).json({ error: 'Operation not found' });
    }
    
    res.json(transformOperation(operation));
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch operation' });
  }
});

// Create new operation
router.post('/', (req, res) => {
  console.log('Received operation request:', JSON.stringify(req.body, null, 2)); // Debug log
  const { customer, shoes, status, totalAmount, isNoCharge, isDoOver, isDelivery, isPickup, notes } = req.body;
  const now = new Date().toISOString();

  if (!customer || !customer.id) {
    console.error('Invalid customer data:', customer);
    return res.status(400).json({ error: 'Invalid customer data' });
  }

  if (!Array.isArray(shoes) || shoes.length === 0) {
    console.error('Invalid shoes data:', shoes);
    return res.status(400).json({ error: 'Invalid shoes data' });
  }

  try {
    const result = db.transaction(() => {
      // Insert the operation
      const operationId = uuidv4();
      console.log('Creating operation with ID:', operationId); // Debug log

      const operationStmt = db.prepare(`
        INSERT INTO operations (
          id, customer_id, status, total_amount, notes, 
          is_no_charge, is_do_over, is_delivery, is_pickup,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      try {
        operationStmt.run(
          operationId,
          customer.id,
          status || 'pending',
          totalAmount || 0,
          notes || null,
          isNoCharge ? 1 : 0,
          isDoOver ? 1 : 0,
          isDelivery ? 1 : 0,
          isPickup ? 1 : 0,
          now,
          now
        );
      } catch (error) {
        console.error('Error inserting operation:', error);
        throw new Error(`Failed to insert operation: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Insert each shoe
      shoes.forEach((shoe, index) => {
        console.log(`Processing shoe ${index + 1}:`, JSON.stringify(shoe, null, 2)); // Debug log
        const shoeId = uuidv4();
        
        try {
          db.prepare(`
            INSERT INTO operation_shoes (
              id, operation_id, category, color, notes, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            shoeId,
            operationId,
            shoe.category,
            shoe.color || null,
            shoe.notes || null,
            now,
            now
          );

          // Insert services for each shoe
          if (Array.isArray(shoe.services)) {
            shoe.services.forEach((service, sIndex) => {
              console.log(`Processing service ${sIndex + 1} for shoe ${index + 1}:`, JSON.stringify(service, null, 2)); // Debug log
              
              try {
                if (!service.service_id) {
                  throw new Error(`Missing service_id for service ${sIndex + 1} of shoe ${index + 1}`);
                }

                db.prepare(`
                  INSERT INTO operation_services (
                    id, operation_shoe_id, service_id, quantity, price, notes,
                    created_at, updated_at
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `).run(
                  uuidv4(),
                  shoeId,
                  service.service_id,
                  service.quantity || 1,
                  service.price || 0,
                  service.notes || null,
                  now,
                  now
                );
              } catch (error) {
                console.error(`Error inserting service ${sIndex + 1} for shoe ${index + 1}:`, error);
                throw error;
              }
            });
          }
        } catch (error) {
          console.error(`Error processing shoe ${index + 1}:`, error);
          throw error;
        }
      });

      // Return the created operation with all related data
      const operation = db.prepare(`
        SELECT 
          o.*,
          c.name as customer_name,
          c.phone as customer_phone,
          c.email as customer_email
        FROM operations o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.id = ?
      `).get(operationId);

      // Get shoes for this operation
      const operationShoes = db.prepare(`
        SELECT * FROM operation_shoes WHERE operation_id = ?
      `).all(operationId);

      // Get services for each shoe
      const shoesWithServices = operationShoes.map(shoe => {
        const services = db.prepare(`
          SELECT 
            os.*,
            s.name as service_name,
            s.price as service_base_price
          FROM operation_services os
          LEFT JOIN services s ON os.service_id = s.id
          WHERE os.operation_shoe_id = ?
        `).all(shoe.id);

        return {
          ...shoe,
          services: services.map(s => ({
            id: s.service_id,
            name: s.service_name,
            price: s.price,
            quantity: s.quantity,
            notes: s.notes
          }))
        };
      });

      return {
        ...operation,
        shoes: shoesWithServices,
        isNoCharge: Boolean(operation.is_no_charge),
        isDoOver: Boolean(operation.is_do_over),
        isDelivery: Boolean(operation.is_delivery),
        isPickup: Boolean(operation.is_pickup)
      };
    })();

    res.json(result);
  } catch (error) {
    console.error('Error creating operation:', error);
    res.status(500).json({ error: error.message || 'Failed to create operation' });
  }
});

// Update operation status
router.patch('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    const now = new Date().toISOString();
    
    const setClauses = Object.keys(updates)
      .map(key => {
        const dbKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        return `${dbKey} = ?`;
      })
      .concat(['updated_at = ?'])
      .join(', ');
    
    const values = [...Object.values(updates), now, id];
    
    db.prepare(`
      UPDATE operations
      SET ${setClauses}
      WHERE id = ?
    `).run(...values);
    
    const operation = db.prepare(`
      SELECT o.*, c.name as customer_name, c.phone as customer_phone
      FROM operations o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.id = ?
    `).get(id);
    
    res.json(transformOperation(operation));
  } catch (error) {
    res.status(500).json({ error: 'Failed to update operation' });
  }
});

export default router;
