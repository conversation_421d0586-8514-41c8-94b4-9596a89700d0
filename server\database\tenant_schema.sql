-- Multi-Tenancy Database Schema
-- This schema handles tenant management and user isolation

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Tenants table - Core tenant information
CREATE TABLE IF NOT EXISTS tenants (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    subdomain TEXT UNIQUE NOT NULL,
    plan_type TEXT NOT NULL DEFAULT 'basic' CHECK(plan_type IN ('trial', 'basic', 'professional', 'enterprise')),
    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'suspended', 'inactive', 'pending')),
    settings JSON NOT NULL DEFAULT '{}',
    owner_email TEXT,
    billing_email TEXT,
    max_users INTEGER DEFAULT 5,
    max_operations INTEGER DEFAULT 1000,
    storage_limit_mb INTEGER DEFAULT 100,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Tenant users table - Users associated with tenants
CREATE TABLE IF NOT EXISTS tenant_users (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    email TEXT NOT NULL,
    password_hash TEXT,
    role TEXT NOT NULL DEFAULT 'staff' CHECK(role IN ('owner', 'admin', 'manager', 'staff', 'viewer')),
    permissions JSON DEFAULT '[]',
    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive', 'pending')),
    profile JSON DEFAULT '{}',
    firebase_uid TEXT UNIQUE,
    last_login TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE(tenant_id, email)
);

-- Tenant invitations table - For inviting users to tenants
CREATE TABLE IF NOT EXISTS tenant_invitations (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    email TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'staff' CHECK(role IN ('owner', 'admin', 'manager', 'staff', 'viewer')),
    invited_by TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK(status IN ('pending', 'accepted', 'expired', 'cancelled')),
    token TEXT UNIQUE NOT NULL,
    expires_at TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES tenant_users(id) ON DELETE CASCADE
);

-- Tenant usage tracking table - For billing and analytics
CREATE TABLE IF NOT EXISTS tenant_usage (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    period TEXT NOT NULL, -- YYYY-MM format
    users_count INTEGER DEFAULT 0,
    operations_count INTEGER DEFAULT 0,
    storage_used_mb INTEGER DEFAULT 0,
    api_calls INTEGER DEFAULT 0,
    last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE(tenant_id, period)
);

-- Tenant subscriptions table - For billing management
CREATE TABLE IF NOT EXISTS tenant_subscriptions (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    plan_type TEXT NOT NULL CHECK(plan_type IN ('trial', 'basic', 'professional', 'enterprise')),
    status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'cancelled', 'past_due', 'unpaid')),
    current_period_start TEXT NOT NULL,
    current_period_end TEXT NOT NULL,
    billing_cycle TEXT NOT NULL DEFAULT 'monthly' CHECK(billing_cycle IN ('monthly', 'yearly')),
    amount REAL NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    stripe_subscription_id TEXT UNIQUE,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- Tenant database registry - Tracks which database file belongs to which tenant
CREATE TABLE IF NOT EXISTS tenant_databases (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL UNIQUE,
    database_path TEXT NOT NULL,
    database_size_mb REAL DEFAULT 0,
    last_backup TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
);

-- Tenant audit log - Track important tenant-level changes
CREATE TABLE IF NOT EXISTS tenant_audit_log (
    id TEXT PRIMARY KEY,
    tenant_id TEXT NOT NULL,
    user_id TEXT,
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT,
    old_values JSON,
    new_values JSON,
    ip_address TEXT,
    user_agent TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES tenant_users(id) ON DELETE SET NULL
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain);
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);
CREATE INDEX IF NOT EXISTS idx_tenants_plan_type ON tenants(plan_type);

CREATE INDEX IF NOT EXISTS idx_tenant_users_tenant ON tenant_users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_users_email ON tenant_users(email);
CREATE INDEX IF NOT EXISTS idx_tenant_users_firebase_uid ON tenant_users(firebase_uid);
CREATE INDEX IF NOT EXISTS idx_tenant_users_status ON tenant_users(status);

CREATE INDEX IF NOT EXISTS idx_tenant_invitations_tenant ON tenant_invitations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_invitations_email ON tenant_invitations(email);
CREATE INDEX IF NOT EXISTS idx_tenant_invitations_token ON tenant_invitations(token);
CREATE INDEX IF NOT EXISTS idx_tenant_invitations_status ON tenant_invitations(status);

CREATE INDEX IF NOT EXISTS idx_tenant_usage_tenant ON tenant_usage(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_usage_period ON tenant_usage(period);

CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_tenant ON tenant_subscriptions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_subscriptions_status ON tenant_subscriptions(status);

CREATE INDEX IF NOT EXISTS idx_tenant_databases_tenant ON tenant_databases(tenant_id);

CREATE INDEX IF NOT EXISTS idx_tenant_audit_log_tenant ON tenant_audit_log(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_audit_log_user ON tenant_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_tenant_audit_log_action ON tenant_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_tenant_audit_log_created_at ON tenant_audit_log(created_at);

-- Create triggers for automatic timestamp updates
CREATE TRIGGER IF NOT EXISTS update_tenants_updated_at
    AFTER UPDATE ON tenants
    FOR EACH ROW
BEGIN
    UPDATE tenants SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_tenant_users_updated_at
    AFTER UPDATE ON tenant_users
    FOR EACH ROW
BEGIN
    UPDATE tenant_users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_tenant_subscriptions_updated_at
    AFTER UPDATE ON tenant_subscriptions
    FOR EACH ROW
BEGIN
    UPDATE tenant_subscriptions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_tenant_databases_updated_at
    AFTER UPDATE ON tenant_databases
    FOR EACH ROW
BEGIN
    UPDATE tenant_databases SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Insert default system tenant (for backward compatibility and system operations)
INSERT OR IGNORE INTO tenants (
    id, 
    name, 
    subdomain, 
    plan_type, 
    status, 
    settings,
    owner_email,
    max_users,
    max_operations,
    storage_limit_mb
) VALUES (
    'default',
    'Default Tenant',
    'default',
    'enterprise',
    'active',
    '{"businessInfo":{"name":"Default Tenant"},"features":{"inventory":true,"reporting":true,"multiLocation":true,"customBranding":false,"apiAccess":true,"advancedAnalytics":true},"preferences":{"timezone":"UTC","currency":"USD","dateFormat":"MM/DD/YYYY","language":"en","theme":"light"},"integrations":{"payment":[],"shipping":[],"accounting":[],"notifications":{"email":true,"sms":false,"whatsapp":false}},"limits":{"maxUsers":-1,"maxOperations":-1,"storageLimitMB":-1}}',
    '<EMAIL>',
    -1,
    -1,
    -1
);

-- Register default tenant database
INSERT OR IGNORE INTO tenant_databases (
    id,
    tenant_id,
    database_path,
    database_size_mb
) VALUES (
    'default_db',
    'default',
    'shoe_repair_pos.db',
    0
);
