import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Truck, 
  Search, 
  Filter, 
  Download, 
  Phone, 
  Calendar, 
  RefreshCw,
  MapPin,
  CheckCircle,
  Clock,
  Package
} from 'lucide-react';
import { Link } from 'react-router-dom';
import type { RepairItem } from '../types';
import { mockPickups } from '../data/mockData';
import { format, parseISO } from 'date-fns';

// Extended RepairItem with delivery details
interface DeliveryItem extends RepairItem {
  deliveryAddress?: string;
  deliveryDate?: string;
  deliveryStatus?: 'scheduled' | 'in-transit' | 'delivered';
  deliveryFee?: number;
}

// In a real app, you would fetch this data from your API
const fetchDeliveryItems = (): Promise<DeliveryItem[]> => {
  return new Promise((resolve) => {
    // Simulate API call delay
    setTimeout(() => {
      // Mock delivery items based on ready-for-pickup items
      const deliveryItems: DeliveryItem[] = mockPickups.map(item => ({
        ...item,
        deliveryAddress: '123 Main St, Kampala, Uganda',
        deliveryDate: format(new Date(new Date().setDate(new Date().getDate() + 1)), 'yyyy-MM-dd'),
        deliveryStatus: Math.random() > 0.7 ? 'in-transit' : Math.random() > 0.4 ? 'scheduled' : 'delivered',
        deliveryFee: 10000
      }));
      
      resolve(deliveryItems);
    }, 800);
  });
};

interface DeliveryStats {
  totalDeliveries: number;
  scheduled: number;
  inTransit: number;
  delivered: number;
  totalRevenue: number;
}

const DeliveryPage: React.FC = () => {
  const [items, setItems] = useState<DeliveryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<DeliveryItem[]>([]);
  const [stats, setStats] = useState<DeliveryStats>({
    totalDeliveries: 0,
    scheduled: 0,
    inTransit: 0,
    delivered: 0,
    totalRevenue: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await fetchDeliveryItems();
        setItems(data);
        
        // Calculate stats
        const scheduled = data.filter(item => item.deliveryStatus === 'scheduled').length;
        const inTransit = data.filter(item => item.deliveryStatus === 'in-transit').length;
        const delivered = data.filter(item => item.deliveryStatus === 'delivered').length;
        const totalRevenue = data.reduce((sum, item) => sum + (item.deliveryFee || 0), 0);
        
        setStats({
          totalDeliveries: data.length,
          scheduled,
          inTransit,
          delivered,
          totalRevenue
        });
        
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [refreshKey]);

  // Filter items based on search query and status filter
  useEffect(() => {
    let result = items;
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item => 
        item.customerName.toLowerCase().includes(query) || 
        item.description.toLowerCase().includes(query) ||
        item.contactNumber.includes(query) ||
        (item.deliveryAddress && item.deliveryAddress.toLowerCase().includes(query))
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(item => item.deliveryStatus === statusFilter);
    }
    
    setFilteredItems(result);
  }, [items, searchQuery, statusFilter]);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleExport = () => {
    // In a real app, you would implement CSV export functionality
    alert('Export functionality would be implemented here');
  };

  const getStatusBadgeColor = (status?: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-transit':
        return 'bg-blue-100 text-blue-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock size={14} className="mr-1" />;
      case 'in-transit':
        return <Truck size={14} className="mr-1" />;
      case 'delivered':
        return <CheckCircle size={14} className="mr-1" />;
      default:
        return <Package size={14} className="mr-1" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const handleUpdateStatus = (id: string, newStatus: 'scheduled' | 'in-transit' | 'delivered') => {
    // In a real app, you would update the delivery status in your database
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === id ? { ...item, deliveryStatus: newStatus } : item
      )
    );
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <Link to="/" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeft size={24} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Deliveries</h1>
            <p className="text-gray-500">Manage item deliveries to customers</p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleRefresh}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <RefreshCw size={18} className="mr-2" />
            Refresh
          </button>
          
          <button
            onClick={handleExport}
            className="flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <Download size={18} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {/* Stats Cards */}
      {loading ? (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-indigo-500">
            <p className="text-gray-500 text-sm mb-1">Total Deliveries</p>
            <p className="text-2xl font-bold text-indigo-600">{stats.totalDeliveries}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-yellow-500">
            <p className="text-gray-500 text-sm mb-1">Scheduled</p>
            <p className="text-2xl font-bold text-yellow-600">{stats.scheduled}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-blue-500">
            <p className="text-gray-500 text-sm mb-1">In Transit</p>
            <p className="text-2xl font-bold text-blue-600">{stats.inTransit}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
            <p className="text-gray-500 text-sm mb-1">Delivered</p>
            <p className="text-2xl font-bold text-green-600">{stats.delivered}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-purple-500">
            <p className="text-gray-500 text-sm mb-1">Revenue</p>
            <p className="text-2xl font-bold text-purple-600">{formatCurrency(stats.totalRevenue)}</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by customer name, address or phone..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="w-full md:w-64">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="scheduled">Scheduled</option>
              <option value="in-transit">In Transit</option>
              <option value="delivered">Delivered</option>
            </select>
          </div>
        </div>
      </div>

      {/* Items List */}
      {loading ? (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : filteredItems.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <Truck size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">No deliveries found</h3>
          <p className="text-gray-500 mb-4">
            There are no deliveries scheduled, or your search filters don't match any items.
          </p>
          <button
            onClick={() => {
              setSearchQuery('');
              setStatusFilter('all');
            }}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Clear Filters
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Delivery Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Delivery Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Fee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.customerName}</div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Phone size={14} className="mr-1" />
                        {item.contactNumber}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 capitalize">{item.type}</div>
                      <div className="text-sm text-gray-500">{item.description}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center text-sm text-gray-500">
                        <MapPin size={14} className="mr-1 flex-shrink-0" />
                        <span className="truncate max-w-xs">{item.deliveryAddress}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar size={14} className="mr-1" />
                        {item.deliveryDate ? format(parseISO(item.deliveryDate), 'MMM dd, yyyy') : 'Not scheduled'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(item.deliveryStatus)}`}>
                        {getStatusIcon(item.deliveryStatus)}
                        {item.deliveryStatus ? item.deliveryStatus.replace('-', ' ') : 'Unknown'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(item.deliveryFee || 0)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                        onClick={() => alert(`View details for ${item.customerName}`)}
                      >
                        View
                      </button>
                      {item.deliveryStatus !== 'delivered' && (
                        <select
                          className="text-sm border border-gray-300 rounded px-2 py-1"
                          value={item.deliveryStatus}
                          onChange={(e) => handleUpdateStatus(
                            item.id, 
                            e.target.value as 'scheduled' | 'in-transit' | 'delivered'
                          )}
                        >
                          <option value="scheduled">Scheduled</option>
                          <option value="in-transit">In Transit</option>
                          <option value="delivered">Delivered</option>
                        </select>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliveryPage;
