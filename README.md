# Shoe Repair POS System

A modern Point of Sale system specifically designed for shoe repair businesses. Built with React, TypeScript, and Node.js.

## Features

- Customer Management
- Repair Order Tracking
- Service Management
- Inventory Control
- Payment Processing
- Reporting and Analytics

## Tech Stack

- Frontend:
  - React
  - TypeScript
  - Vite
  - TailwindCSS
  - React Context for State Management

- Backend:
  - Node.js
  - Express
  - SQLite
  - TypeScript

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/Humpyt/ShoeRepairPOS.git
cd ShoeRepairPOS
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3000

## Project Structure

- `/src` - Frontend React application
  - `/components` - React components
  - `/contexts` - React context providers
  - `/pages` - Page components
  - `/services` - API services
  - `/types` - TypeScript type definitions

- `/server` - Backend Node.js application
  - `index.ts` - Main server file
  - `database.ts` - Database configuration
  - `operations.ts` - Operations routes
  - `utils.ts` - Utility functions

## License

MIT License
