{"name": "shoemax-repair-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "vite", "dev:server": "tsx watch server/index.ts", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "init:supplies": "tsx server/init_supplies_table.ts", "add:supplies": "tsx server/add_dummy_supplies.ts", "create:admin": "tsx scripts/create-admin-user.ts"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/free-regular-svg-icons": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^1.7.19", "@mui/icons-material": "^6.1.10", "@mui/lab": "^6.0.0-beta.23", "@mui/material": "^6.1.10", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@types/better-sqlite3": "^7.6.12", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/firebase": "^2.4.32", "@types/react-datepicker": "^6.2.0", "@types/uuid": "^9.0.8", "axios": "^1.7.9", "better-sqlite3": "^9.6.0", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^8.2.2", "cors": "^2.8.5", "date-fns": "^2.30.0", "escpos": "^3.0.0-alpha.6", "escpos-usb": "^3.0.0-alpha.4", "express": "^4.21.2", "firebase": "^11.1.0", "lucide-react": "^0.294.0", "node-thermal-printer": "^4.4.3", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-barcode": "^1.5.3", "react-calendar": "^4.8.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.1", "react-router-dom": "^7.1.1", "recharts": "^2.12.2", "sqlite3": "^5.1.7", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.2", "usb": "^2.14.0", "uuid": "^9.0.1", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}