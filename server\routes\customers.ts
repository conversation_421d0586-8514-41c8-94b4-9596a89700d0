import express from 'express';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Get all customers (tenant-aware)
router.get('/', async (req, res) => {
  try {
    // Use tenant database if available, otherwise fall back to default
    const database = req.tenantDb || req.app.locals.db;
    
    const customers = database.prepare(`
      SELECT * FROM customers
      WHERE status != 'inactive'
      ORDER BY name ASC
    `).all();
    
    res.json(customers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
});

// Get customer by ID (tenant-aware)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const database = req.tenantDb || req.app.locals.db;
    
    const customer = database.prepare(`
      SELECT * FROM customers WHERE id = ? AND status != 'inactive'
    `).get(id);
    
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }
    
    res.json(customer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({ error: 'Failed to fetch customer' });
  }
});

// Create new customer (tenant-aware)
router.post('/', async (req, res) => {
  try {
    const { name, phone, email, address, notes } = req.body;
    
    if (!name || !phone) {
      return res.status(400).json({ error: 'Name and phone are required' });
    }
    
    const database = req.tenantDb || req.app.locals.db;
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const result = database.prepare(`
      INSERT INTO customers (
        id, name, phone, email, address, notes, 
        status, total_orders, total_spent, 
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'active', 0, 0, ?, ?)
    `).run(id, name, phone, email || null, address || null, notes || null, now, now);
    
    if (result.changes > 0) {
      const customer = database.prepare('SELECT * FROM customers WHERE id = ?').get(id);
      res.status(201).json(customer);
    } else {
      res.status(400).json({ error: 'Failed to create customer' });
    }
  } catch (error) {
    console.error('Error creating customer:', error);
    
    // Handle unique constraint violations
    if (error.message.includes('UNIQUE constraint failed')) {
      if (error.message.includes('phone')) {
        return res.status(400).json({ error: 'Phone number already exists' });
      }
      if (error.message.includes('email')) {
        return res.status(400).json({ error: 'Email already exists' });
      }
    }
    
    res.status(500).json({ error: 'Failed to create customer' });
  }
});

// Update customer (tenant-aware)
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    const database = req.tenantDb || req.app.locals.db;
    
    // Check if customer exists
    const existingCustomer = database.prepare(`
      SELECT * FROM customers WHERE id = ? AND status != 'inactive'
    `).get(id);
    
    if (!existingCustomer) {
      return res.status(404).json({ error: 'Customer not found' });
    }
    
    // Build dynamic update query
    const allowedFields = ['name', 'phone', 'email', 'address', 'notes'];
    const updateFields = Object.keys(updates).filter(key => allowedFields.includes(key));
    
    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No valid fields to update' });
    }
    
    const setClauses = updateFields.map(field => `${field} = ?`);
    setClauses.push('updated_at = ?');
    
    const values = updateFields.map(field => updates[field]);
    values.push(new Date().toISOString());
    values.push(id);
    
    const result = database.prepare(`
      UPDATE customers
      SET ${setClauses.join(', ')}
      WHERE id = ?
    `).run(...values);
    
    if (result.changes > 0) {
      const customer = database.prepare('SELECT * FROM customers WHERE id = ?').get(id);
      res.json(customer);
    } else {
      res.status(404).json({ error: 'Customer not found' });
    }
  } catch (error) {
    console.error('Error updating customer:', error);
    
    // Handle unique constraint violations
    if (error.message.includes('UNIQUE constraint failed')) {
      if (error.message.includes('phone')) {
        return res.status(400).json({ error: 'Phone number already exists' });
      }
      if (error.message.includes('email')) {
        return res.status(400).json({ error: 'Email already exists' });
      }
    }
    
    res.status(500).json({ error: 'Failed to update customer' });
  }
});

// Soft delete customer (tenant-aware)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const database = req.tenantDb || req.app.locals.db;
    const now = new Date().toISOString();
    
    const result = database.prepare(`
      UPDATE customers 
      SET status = 'inactive', updated_at = ? 
      WHERE id = ? AND status != 'inactive'
    `).run(now, id);
    
    if (result.changes > 0) {
      res.status(204).send();
    } else {
      res.status(404).json({ error: 'Customer not found' });
    }
  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(500).json({ error: 'Failed to delete customer' });
  }
});

// Search customers (tenant-aware)
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const database = req.tenantDb || req.app.locals.db;
    
    const customers = database.prepare(`
      SELECT * FROM customers
      WHERE status != 'inactive'
        AND (
          name LIKE ? OR 
          phone LIKE ? OR 
          email LIKE ?
        )
      ORDER BY name ASC
      LIMIT 50
    `).all(`%${query}%`, `%${query}%`, `%${query}%`);
    
    res.json(customers);
  } catch (error) {
    console.error('Error searching customers:', error);
    res.status(500).json({ error: 'Failed to search customers' });
  }
});

// Get customer statistics (tenant-aware)
router.get('/:id/stats', async (req, res) => {
  try {
    const { id } = req.params;
    const database = req.tenantDb || req.app.locals.db;
    
    // Get customer basic info
    const customer = database.prepare(`
      SELECT * FROM customers WHERE id = ? AND status != 'inactive'
    `).get(id);
    
    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }
    
    // Get operation statistics
    const operationStats = database.prepare(`
      SELECT 
        COUNT(*) as total_operations,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_operations,
        COUNT(CASE WHEN status = 'in-progress' THEN 1 END) as in_progress_operations,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_operations,
        SUM(total_amount) as total_spent,
        AVG(total_amount) as average_order_value
      FROM operations
      WHERE customer_id = ?
    `).get(id);
    
    // Get recent operations
    const recentOperations = database.prepare(`
      SELECT id, status, total_amount, created_at, promised_date
      FROM operations
      WHERE customer_id = ?
      ORDER BY created_at DESC
      LIMIT 5
    `).all(id);
    
    res.json({
      customer: {
        id: customer.id,
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        created_at: customer.created_at,
        last_visit: customer.last_visit
      },
      statistics: operationStats,
      recent_operations: recentOperations
    });
  } catch (error) {
    console.error('Error fetching customer stats:', error);
    res.status(500).json({ error: 'Failed to fetch customer statistics' });
  }
});

export default router;
