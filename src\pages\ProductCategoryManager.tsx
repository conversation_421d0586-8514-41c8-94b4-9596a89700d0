import React, { useState, useRef, useEffect } from 'react';
import { PlusCircle, Edit2, Trash2, ArrowLeft, Image as ImageIcon, DollarSign, Tag, Box } from 'lucide-react';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import { useProducts, Category, Product } from '../contexts/ProductContext';
import './ProductCategoryManager.css';

interface ImageUploadProps {
  onImageSelect: (file: File) => void;
  currentImageUrl?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onImageSelect, currentImageUrl }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImageSelect(file);
    }
  };

  return (
    <div
      onClick={handleImageClick}
      className="relative w-full h-48 bg-indigo-900/50 rounded-lg cursor-pointer hover:bg-indigo-800/50 transition-colors"
    >
      {currentImageUrl ? (
        <img
          src={currentImageUrl}
          alt="Product"
          className="w-full h-full object-cover rounded-lg"
        />
      ) : (
        <div className="flex flex-col items-center justify-center h-full">
          <ImageIcon size={40} className="text-indigo-300 mb-2" />
          <span className="text-indigo-300">Click to upload image</span>
        </div>
      )}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

const ProductCategoryManager: React.FC = () => {
  const {
    categories,
    addCategory,
    updateCategory,
    deleteCategory,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductsByCategory
  } = useProducts();

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [isAddingProduct, setIsAddingProduct] = useState(false);

  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    imageUrl: ''
  });

  const [newProduct, setNewProduct] = useState({
    name: '',
    price: 0,
    description: '',
    imageUrl: '',
    categoryId: '',
    inStock: true,
    featured: false
  });

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingProductData, setEditingProductData] = useState<Product | null>(null);

  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const [isDeleteCategoryModalOpen, setIsDeleteCategoryModalOpen] = useState(false);

  const [isEditCategoryModalOpen, setIsEditCategoryModalOpen] = useState(false);
  const [editingCategoryData, setEditingCategoryData] = useState<Category | null>(null);

  const handleImageUpload = async (file: File) => {
    // Revoke any previous object URLs to prevent memory leaks
    if (isAddingCategory) {
      if (newCategory.imageUrl && newCategory.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(newCategory.imageUrl);
      }
    } else if (isAddingProduct) {
      if (newProduct.imageUrl && newProduct.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(newProduct.imageUrl);
      }
    } else if (editingProductData) {
      if (editingProductData.imageUrl && editingProductData.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(editingProductData.imageUrl);
      }
    }

    const imageUrl = URL.createObjectURL(file);
    if (isAddingCategory) {
      setNewCategory(prev => ({ ...prev, imageUrl }));
    } else if (isAddingProduct) {
      setNewProduct(prev => ({ ...prev, imageUrl }));
    } else if (editingProductData) {
      setEditingProductData(prev => prev ? { ...prev, imageUrl } : null);
    }
  };

  const handleAddCategory = () => {
    if (!newCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    addCategory(newCategory);
    setNewCategory({ name: '', description: '', imageUrl: '' });
    setIsAddingCategory(false);
  };

  const handleAddProduct = () => {
    if (!selectedCategory) {
      toast.error('Please select a category first');
      return;
    }
    if (!newProduct.name.trim()) {
      toast.error('Product name is required');
      return;
    }
    if (newProduct.price <= 0) {
      toast.error('Price must be greater than 0');
      return;
    }

    addProduct({ ...newProduct, categoryId: selectedCategory });
    toast.success('Product added successfully');
    setNewProduct({
      name: '',
      price: 0,
      description: '',
      imageUrl: '',
      categoryId: '',
      inStock: true,
      featured: false
    });
    setIsAddingProduct(false);
  };

  const handleDeleteProduct = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = () => {
    if (productToDelete) {
      deleteProduct(productToDelete.id);
      toast.success('Product deleted successfully');
      setIsDeleteModalOpen(false);
      setProductToDelete(null);
    }
  };

  const handleEditProduct = (product: Product) => {
    setEditingProductData(product);
    setIsEditModalOpen(true);
  };

  const handleUpdateProduct = () => {
    if (editingProductData) {
      updateProduct(editingProductData.id, editingProductData);
      toast.success('Product updated successfully');
      setIsEditModalOpen(false);
      setEditingProductData(null);
    }
  };

  const handleDeleteCategory = (category: Category) => {
    setCategoryToDelete(category);
    setIsDeleteCategoryModalOpen(true);
  };

  const confirmDeleteCategory = () => {
    if (categoryToDelete) {
      deleteCategory(categoryToDelete.id);
      toast.success('Category deleted successfully');
      setIsDeleteCategoryModalOpen(false);
      setCategoryToDelete(null);
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategoryData({...category});
    setIsEditCategoryModalOpen(true);
  };

  const handleUpdateCategory = () => {
    if (editingCategoryData) {
      updateCategory(editingCategoryData.id, editingCategoryData);
      toast.success('Category updated successfully');
      setIsEditCategoryModalOpen(false);
      setEditingCategoryData(null);
    }
  };

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category.id);
  };

  // Cleanup blob URLs when component unmounts
  useEffect(() => {
    return () => {
      // Cleanup for category images
      if (newCategory.imageUrl && newCategory.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(newCategory.imageUrl);
      }
      
      // Cleanup for product images
      if (newProduct.imageUrl && newProduct.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(newProduct.imageUrl);
      }
      
      // Cleanup for editing product images
      if (editingProductData?.imageUrl && editingProductData.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(editingProductData.imageUrl);
      }
    };
  }, [newCategory.imageUrl, newProduct.imageUrl, editingProductData?.imageUrl]);

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link to="/sales-items" className="text-indigo-300 hover:text-indigo-200 transition-colors">
            <ArrowLeft size={24} />
          </Link>
          <h1 className="text-2xl font-bold text-white">Product & Category Management</h1>
        </div>
        <div className="flex gap-4">
          <button
            onClick={() => setIsAddingCategory(true)}
            className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <Tag size={20} />
            Add Category
          </button>
          {categories.length > 0 ? (
            <button
              onClick={() => {
                if (!selectedCategory) {
                  toast.error('Please click on a category first to select it');
                  return;
                }
                setIsAddingProduct(true);
              }}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                selectedCategory 
                  ? 'bg-indigo-500 text-white hover:bg-indigo-600' 
                  : 'bg-indigo-500/50 text-indigo-200'
              }`}
            >
              <Box size={20} />
              {selectedCategory ? 'Add Product' : 'Select a Category First'}
            </button>
          ) : null}
        </div>
      </div>

      <div className="grid grid-cols-12 gap-6">
        {/* Categories Sidebar */}
        <div className="col-span-3 bg-indigo-900/30 rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4 text-indigo-200">Categories</h2>
          <div className="space-y-3">
            {categories.length === 0 ? (
              <div className="empty-state">
                <div className="text-indigo-400 mx-auto mb-3">No categories found</div>
                <button
                  onClick={() => setIsAddingCategory(true)}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  <Tag size={20} />
                  Add First Category
                </button>
              </div>
            ) : (
              categories.map(category => (
                <div
                  key={category.id}
                  onClick={() => handleCategoryClick(category)}
                  className={`group relative p-4 rounded-lg cursor-pointer transition-all ${
                    selectedCategory === category.id
                      ? 'bg-indigo-600/40 ring-2 ring-indigo-500'
                      : 'bg-indigo-800/20 hover:bg-indigo-800/30'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    {category.imageUrl && (
                      <img
                        src={category.imageUrl}
                        alt={category.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    )}
                    <div className="flex-1">
                      <h3 className="font-medium text-indigo-100">{category.name}</h3>
                      {category.description && (
                        <p className="text-sm text-indigo-300">{category.description}</p>
                      )}
                    </div>
                    {selectedCategory === category.id && (
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 text-indigo-300">
                        <PlusCircle size={20} />
                      </div>
                    )}
                  </div>
                  <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={() => handleEditCategory(category)}
                      className="p-1.5 text-indigo-300 hover:text-indigo-100 hover:bg-indigo-700/50 rounded-full transition-colors"
                    >
                      <Edit2 size={16} />
                    </button>
                    <button
                      onClick={() => handleDeleteCategory(category)}
                      className="p-1.5 text-red-300 hover:text-red-100 hover:bg-red-700/50 rounded-full transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Main Content Area */}
        <div className="col-span-9 space-y-6">
          {/* Selected Category Info */}
          {selectedCategory && !isAddingCategory && !isAddingProduct && (
            <div className="bg-indigo-900/30 rounded-lg p-4 flex justify-between items-center">
              <div className="flex items-center gap-3">
                <div className="text-indigo-200">
                  <h3 className="font-medium">Selected Category:</h3>
                  <p className="text-lg text-indigo-100">
                    {categories.find(c => c.id === selectedCategory)?.name}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setIsAddingProduct(true)}
                className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
              >
                <Box size={20} />
                Add Product to Category
              </button>
            </div>
          )}

          {/* Add/Edit Forms */}
          {(isAddingCategory || isAddingProduct) && (
            <div className="bg-indigo-900/30 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-indigo-200">
                {isAddingCategory ? 'Add New Category' : 'Add New Product'}
              </h2>
              
              {isAddingCategory ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-indigo-200">Name</label>
                      <input
                        type="text"
                        value={newCategory.name}
                        onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                        className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                        placeholder="Enter category name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-indigo-200">Description</label>
                      <input
                        type="text"
                        value={newCategory.description}
                        onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                        className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                        placeholder="Enter category description"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Category Image</label>
                    <ImageUpload
                      onImageSelect={handleImageUpload}
                      currentImageUrl={newCategory.imageUrl}
                    />
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={handleAddCategory}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                      Save Category
                    </button>
                    <button
                      onClick={() => {
                        setIsAddingCategory(false);
                        setNewCategory({ name: '', description: '', imageUrl: '' });
                      }}
                      className="px-4 py-2 bg-indigo-800/50 text-white rounded-lg hover:bg-indigo-700/50 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="mb-6 p-3 bg-indigo-800/30 rounded-lg">
                    <p className="text-indigo-200">
                      Adding product to category:{' '}
                      <span className="font-medium text-indigo-100">
                        {categories.find(c => c.id === selectedCategory)?.name}
                      </span>
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-indigo-200">Name</label>
                      <input
                        type="text"
                        value={newProduct.name}
                        onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                        className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                        placeholder="Enter product name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-indigo-200">Price</label>
                      <div className="relative">
                        <span className="absolute left-3 top-2 text-indigo-400">
                          <DollarSign size={20} />
                        </span>
                        <input
                          type="number"
                          value={newProduct.price}
                          onChange={(e) => setNewProduct({ ...newProduct, price: parseFloat(e.target.value) })}
                          className="w-full pl-10 pr-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                          placeholder="0.00"
                          step="0.01"
                          min="0"
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Description</label>
                    <textarea
                      value={newProduct.description}
                      onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                      className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                      placeholder="Enter product description"
                      rows={3}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Product Image</label>
                    <ImageUpload
                      onImageSelect={handleImageUpload}
                      currentImageUrl={newProduct.imageUrl}
                    />
                  </div>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newProduct.inStock}
                        onChange={(e) => setNewProduct({ ...newProduct, inStock: e.target.checked })}
                        className="mr-2 bg-indigo-800/50 border-indigo-500"
                      />
                      <span className="text-indigo-200">In Stock</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newProduct.featured}
                        onChange={(e) => setNewProduct({ ...newProduct, featured: e.target.checked })}
                        className="mr-2 bg-indigo-800/50 border-indigo-500"
                      />
                      <span className="text-indigo-200">Featured</span>
                    </label>
                  </div>
                  <div className="flex justify-end gap-4 mt-6">
                    <button
                      onClick={() => {
                        setIsAddingProduct(false);
                        setNewProduct({
                          name: '',
                          price: 0,
                          description: '',
                          imageUrl: '',
                          categoryId: '',
                          inStock: true,
                          featured: false
                        });
                      }}
                      className="px-4 py-2 bg-indigo-800/50 text-white rounded-lg hover:bg-indigo-700/50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleAddProduct}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                      Add Product
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Products Grid */}
          {selectedCategory && (
            <div className="grid grid-cols-3 gap-6">
              {getProductsByCategory(selectedCategory).map(product => (
                <div
                  key={product.id}
                  className="group bg-indigo-900/30 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <div className="relative h-48">
                    <img
                      src={product.imageUrl || 'https://via.placeholder.com/300x200'}
                      alt={product.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    {product.featured && (
                      <span className="absolute top-2 right-2 bg-yellow-500 text-black px-2 py-1 rounded-full text-sm font-medium">
                        Featured
                      </span>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/0 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-semibold text-white">{product.name}</h3>
                      <span className="text-xl font-bold text-indigo-300">${product.price.toFixed(2)}</span>
                    </div>
                    <p className="text-indigo-200 text-sm mb-4 line-clamp-2">{product.description}</p>
                    <div className="flex items-center justify-between">
                      <span className={`text-sm ${product.inStock ? 'text-green-400' : 'text-red-400'}`}>
                        {product.inStock ? 'In Stock' : 'Out of Stock'}
                      </span>
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="p-2 text-indigo-300 hover:text-white hover:bg-indigo-600/50 rounded-full transition-all duration-300 relative group"
                          title="Edit Product"
                        >
                          <Edit2 size={20} />
                          <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            Edit
                          </span>
                        </button>
                        <button
                          onClick={() => handleDeleteProduct(product)}
                          className="p-2 text-indigo-300 hover:text-red-400 hover:bg-red-600/20 rounded-full transition-all duration-300 relative group"
                          title="Delete Product"
                        >
                          <Trash2 size={20} />
                          <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            Delete
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {isDeleteModalOpen && productToDelete && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-indigo-900/95 rounded-lg p-6 max-w-md w-full mx-4 transform animate-modal-in">
                <h3 className="text-xl font-bold text-white mb-4">Delete Product</h3>
                <p className="text-indigo-200 mb-6">
                  Are you sure you want to delete <span className="font-semibold text-white">{productToDelete.name}</span>? 
                  This action cannot be undone.
                </p>
                <div className="flex justify-end gap-4">
                  <button
                    onClick={() => {
                      setIsDeleteModalOpen(false);
                      setProductToDelete(null);
                    }}
                    className="px-4 py-2 bg-indigo-800/50 text-white rounded-lg hover:bg-indigo-700/50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                  >
                    <Trash2 size={16} />
                    Delete
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Delete Category Confirmation Modal */}
          {isDeleteCategoryModalOpen && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-indigo-900/95 p-6 rounded-lg max-w-md w-full">
                <h3 className="text-xl font-semibold text-red-400 mb-4">Delete Category</h3>
                <p className="text-indigo-100 mb-6">
                  Are you sure you want to delete "{categoryToDelete?.name}"? This action cannot be undone.
                </p>
                <div className="flex justify-end gap-4">
                  <button
                    onClick={() => {
                      setIsDeleteCategoryModalOpen(false);
                      setCategoryToDelete(null);
                    }}
                    className="px-4 py-2 bg-indigo-800/50 text-white rounded-lg hover:bg-indigo-700/50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmDeleteCategory}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Edit Product Modal */}
          {isEditModalOpen && editingProductData && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-indigo-900/95 rounded-lg p-6 max-w-2xl w-full mx-4 transform animate-modal-in">
                <h3 className="text-xl font-bold text-white mb-4">Edit Product</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-indigo-200">Name</label>
                      <input
                        type="text"
                        value={editingProductData.name}
                        onChange={(e) => setEditingProductData({ ...editingProductData, name: e.target.value })}
                        className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-indigo-200">Price</label>
                      <div className="relative">
                        <span className="absolute left-3 top-2 text-indigo-400">
                          <DollarSign size={20} />
                        </span>
                        <input
                          type="number"
                          value={editingProductData.price}
                          onChange={(e) => setEditingProductData({ ...editingProductData, price: parseFloat(e.target.value) })}
                          className="w-full pl-10 pr-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                          step="0.01"
                          min="0"
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Description</label>
                    <textarea
                      value={editingProductData.description}
                      onChange={(e) => setEditingProductData({ ...editingProductData, description: e.target.value })}
                      className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                      rows={3}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Product Image</label>
                    <ImageUpload
                      onImageSelect={handleImageUpload}
                      currentImageUrl={editingProductData.imageUrl}
                    />
                  </div>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={editingProductData.inStock}
                        onChange={(e) => setEditingProductData({ ...editingProductData, inStock: e.target.checked })}
                        className="mr-2 bg-indigo-800/50 border-indigo-500"
                      />
                      <span className="text-indigo-200">In Stock</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={editingProductData.featured}
                        onChange={(e) => setEditingProductData({ ...editingProductData, featured: e.target.checked })}
                        className="mr-2 bg-indigo-800/50 border-indigo-500"
                      />
                      <span className="text-indigo-200">Featured</span>
                    </label>
                  </div>
                  <div className="flex justify-end gap-4 mt-6">
                    <button
                      onClick={() => {
                        setIsEditModalOpen(false);
                        setEditingProductData(null);
                      }}
                      className="px-4 py-2 bg-indigo-800/50 text-white rounded-lg hover:bg-indigo-700/50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUpdateProduct}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
                    >
                      <Edit2 size={16} />
                      Update Product
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Edit Category Modal */}
          {isEditCategoryModalOpen && editingCategoryData && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-indigo-900/95 p-6 rounded-lg max-w-md w-full">
                <h3 className="text-xl font-semibold text-indigo-200 mb-4">Edit Category</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Name</label>
                    <input
                      type="text"
                      value={editingCategoryData.name}
                      onChange={(e) => setEditingCategoryData({ ...editingCategoryData, name: e.target.value })}
                      className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                      placeholder="Enter category name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Description</label>
                    <input
                      type="text"
                      value={editingCategoryData.description}
                      onChange={(e) => setEditingCategoryData({ ...editingCategoryData, description: e.target.value })}
                      className="w-full px-3 py-2 bg-indigo-800/50 rounded-lg focus:ring-2 focus:ring-indigo-500 outline-none text-white"
                      placeholder="Enter category description"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1 text-indigo-200">Category Image</label>
                    <ImageUpload
                      onImageSelect={(file) => {
                        // Revoke previous blob URL if it exists
                        if (editingCategoryData.imageUrl && editingCategoryData.imageUrl.startsWith('blob:')) {
                          URL.revokeObjectURL(editingCategoryData.imageUrl);
                        }
                        const imageUrl = URL.createObjectURL(file);
                        setEditingCategoryData({ ...editingCategoryData, imageUrl });
                      }}
                      currentImageUrl={editingCategoryData.imageUrl}
                    />
                  </div>
                  <div className="flex justify-end gap-4 mt-6">
                    <button
                      onClick={() => {
                        setIsEditCategoryModalOpen(false);
                        setEditingCategoryData(null);
                      }}
                      className="px-4 py-2 bg-indigo-800/50 text-white rounded-lg hover:bg-indigo-700/50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUpdateCategory}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                      Save Changes
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCategoryManager;