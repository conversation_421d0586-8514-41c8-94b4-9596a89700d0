@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-900 text-gray-100;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
      'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  .digital-clock {
    font-family: 'Digital-7', monospace;
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
  }

  .btn-bevel {
    @apply relative overflow-hidden transition-all duration-200 border border-gray-700;
    box-shadow: 
      inset -2px -2px 4px rgba(0, 0, 0, 0.3),
      inset 2px 2px 4px rgba(255, 255, 255, 0.1),
      4px 4px 10px rgba(0, 0, 0, 0.5);
  }

  .btn-bevel:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 
      inset -2px -2px 4px rgba(0, 0, 0, 0.3),
      inset 2px 2px 4px rgba(255, 255, 255, 0.1),
      6px 6px 12px rgba(0, 0, 0, 0.6);
  }

  .btn-bevel:active {
    @apply transform translate-y-0.5;
    box-shadow: 
      inset 2px 2px 4px rgba(0, 0, 0, 0.3),
      inset -2px -2px 4px rgba(255, 255, 255, 0.1),
      2px 2px 6px rgba(0, 0, 0, 0.4);
  }

  .accent-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white;
  }

  .accent-secondary {
    @apply bg-emerald-600 hover:bg-emerald-700 text-white;
  }

  .accent-tertiary {
    @apply bg-amber-600 hover:bg-amber-700 text-white;
  }

  .card-bevel {
    @apply bg-gray-800 rounded-lg border border-gray-700;
    box-shadow: 
      inset -2px -2px 4px rgba(0, 0, 0, 0.2),
      inset 2px 2px 4px rgba(255, 255, 255, 0.05),
      8px 8px 16px rgba(0, 0, 0, 0.3);
  }

  .quick-info-btn {
    @apply btn-bevel bg-gray-800 hover:bg-gray-700 p-4 rounded-lg text-left transition-colors;
  }

  .quick-access-btn {
    @apply btn-bevel bg-gray-800 p-4 rounded-lg flex flex-col items-center justify-center space-y-2 hover:bg-gray-700 transition-colors;
  }

  .top-bar-btn {
    @apply btn-bevel bg-gray-800 hover:bg-gray-700 px-6 py-3 rounded-lg flex items-center space-x-2;
  }
}

@layer utilities {
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-800 rounded;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-700 rounded hover:bg-gray-600;
  }
}