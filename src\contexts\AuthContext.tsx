import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  User,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
} from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';

export type UserRole = 'admin' | 'manager' | 'staff';

interface UserData {
  uid: string;
  email: string;
  role: UserRole;
  name?: string;
}

interface AuthContextType {
  currentUser: User | null;
  userData: UserData | null;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, role: UserRole, name: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  const signup = async (email: string, password: string, role: UserRole, name: string) => {
    console.log('Attempting to sign up:', email);
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    console.log('User created in Authentication:', user.uid);
    
    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      uid: user.uid,
      email: user.email,
      role,
      name,
      createdAt: new Date().toISOString()
    });
    
    console.log('User document created in Firestore');
  };

  const login = async (email: string, password: string) => {
    console.log('Attempting to log in with email only:', email);
    
    try {
      // Bypass Firebase authentication by using the custom server login endpoint
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      const data = await response.json();
      
      if (!data.success) {
        console.error('Login failed: Invalid email');
        throw new Error('Invalid email address');
      }
      
      // Set a mock user for the frontend
      setCurrentUser({
        uid: 'mock-user-id',
        email: email,
        displayName: email.split('@')[0],
      } as any);
      
      // Set mock user data
      setUserData({
        id: 'mock-user-id',
        email: email,
        name: email.split('@')[0],
        role: 'staff',
        createdAt: new Date().toISOString(),
        active: true,
        lastLogin: new Date().toISOString()
      });
      
      console.log('Successfully logged in with email');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = () => signOut(auth);

  const fetchUserData = async (user: User) => {
    console.log('Fetching user data for:', user.uid);
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    if (userDoc.exists()) {
      console.log('User document found:', userDoc.data());
      setUserData(userDoc.data() as UserData);
    } else {
      console.error('No user document found for:', user.uid);
      setUserData(null);
    }
  };

  useEffect(() => {
    console.log('Setting up auth state listener');
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log('Auth state changed:', user?.uid);
      setCurrentUser(user);
      if (user) {
        await fetchUserData(user);
      } else {
        setUserData(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    userData,
    login,
    signup,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
