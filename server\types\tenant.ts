// Tenant-related types and interfaces for multi-tenancy

export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  plan_type: TenantPlan;
  status: TenantStatus;
  settings: TenantSettings;
  created_at: string;
  updated_at: string;
  owner_email?: string;
  billing_email?: string;
  max_users?: number;
  max_operations?: number;
  storage_limit_mb?: number;
}

export type TenantPlan = 'basic' | 'professional' | 'enterprise' | 'trial';

export type TenantStatus = 'active' | 'suspended' | 'inactive' | 'pending';

export interface TenantSettings {
  businessInfo: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    website?: string;
    logo_url?: string;
  };
  features: {
    inventory: boolean;
    reporting: boolean;
    multiLocation: boolean;
    customBranding: boolean;
    apiAccess: boolean;
    advancedAnalytics: boolean;
  };
  preferences: {
    timezone: string;
    currency: string;
    dateFormat: string;
    language: string;
    theme: 'light' | 'dark' | 'auto';
  };
  integrations: {
    payment: string[];
    shipping: string[];
    accounting: string[];
    notifications: {
      email: boolean;
      sms: boolean;
      whatsapp: boolean;
    };
  };
  limits: {
    maxUsers: number;
    maxOperations: number;
    storageLimitMB: number;
  };
}

export interface TenantUser {
  id: string;
  tenant_id: string;
  email: string;
  password_hash?: string;
  role: TenantUserRole;
  permissions: string[];
  status: 'active' | 'inactive' | 'pending';
  profile: {
    first_name?: string;
    last_name?: string;
    phone?: string;
    avatar_url?: string;
  };
  created_at: string;
  updated_at: string;
  last_login?: string;
  firebase_uid?: string; // For Firebase integration
}

export type TenantUserRole = 'owner' | 'admin' | 'manager' | 'staff' | 'viewer';

export interface TenantInvitation {
  id: string;
  tenant_id: string;
  email: string;
  role: TenantUserRole;
  invited_by: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  token: string;
  expires_at: string;
  created_at: string;
}

export interface TenantUsage {
  tenant_id: string;
  period: string; // YYYY-MM format
  users_count: number;
  operations_count: number;
  storage_used_mb: number;
  api_calls: number;
  last_updated: string;
}

export interface TenantSubscription {
  id: string;
  tenant_id: string;
  plan_type: TenantPlan;
  status: 'active' | 'cancelled' | 'past_due' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  billing_cycle: 'monthly' | 'yearly';
  amount: number;
  currency: string;
  stripe_subscription_id?: string;
  created_at: string;
  updated_at: string;
}

// Default tenant settings for new tenants
export const DEFAULT_TENANT_SETTINGS: TenantSettings = {
  businessInfo: {
    name: '',
    address: '',
    phone: '',
    email: '',
    website: '',
    logo_url: ''
  },
  features: {
    inventory: true,
    reporting: true,
    multiLocation: false,
    customBranding: false,
    apiAccess: false,
    advancedAnalytics: false
  },
  preferences: {
    timezone: 'UTC',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    language: 'en',
    theme: 'light'
  },
  integrations: {
    payment: [],
    shipping: [],
    accounting: [],
    notifications: {
      email: true,
      sms: false,
      whatsapp: false
    }
  },
  limits: {
    maxUsers: 5,
    maxOperations: 1000,
    storageLimitMB: 100
  }
};

// Plan-specific feature sets
export const PLAN_FEATURES: Record<TenantPlan, Partial<TenantSettings['features']>> = {
  trial: {
    inventory: true,
    reporting: false,
    multiLocation: false,
    customBranding: false,
    apiAccess: false,
    advancedAnalytics: false
  },
  basic: {
    inventory: true,
    reporting: true,
    multiLocation: false,
    customBranding: false,
    apiAccess: false,
    advancedAnalytics: false
  },
  professional: {
    inventory: true,
    reporting: true,
    multiLocation: true,
    customBranding: true,
    apiAccess: true,
    advancedAnalytics: false
  },
  enterprise: {
    inventory: true,
    reporting: true,
    multiLocation: true,
    customBranding: true,
    apiAccess: true,
    advancedAnalytics: true
  }
};

// Plan-specific limits
export const PLAN_LIMITS: Record<TenantPlan, TenantSettings['limits']> = {
  trial: {
    maxUsers: 2,
    maxOperations: 50,
    storageLimitMB: 10
  },
  basic: {
    maxUsers: 5,
    maxOperations: 1000,
    storageLimitMB: 100
  },
  professional: {
    maxUsers: 25,
    maxOperations: 10000,
    storageLimitMB: 1000
  },
  enterprise: {
    maxUsers: -1, // unlimited
    maxOperations: -1, // unlimited
    storageLimitMB: -1 // unlimited
  }
};

// Utility functions
export function createDefaultTenantSettings(plan: TenantPlan): TenantSettings {
  return {
    ...DEFAULT_TENANT_SETTINGS,
    features: {
      ...DEFAULT_TENANT_SETTINGS.features,
      ...PLAN_FEATURES[plan]
    },
    limits: PLAN_LIMITS[plan]
  };
}

export function validateSubdomain(subdomain: string): boolean {
  // Subdomain validation rules:
  // - 3-63 characters
  // - alphanumeric and hyphens only
  // - cannot start or end with hyphen
  // - cannot contain consecutive hyphens
  const regex = /^[a-z0-9]([a-z0-9-]{1,61}[a-z0-9])?$/;
  return regex.test(subdomain.toLowerCase());
}

export function isSubdomainReserved(subdomain: string): boolean {
  const reserved = [
    'www', 'api', 'app', 'admin', 'dashboard', 'mail', 'email', 'ftp', 'blog',
    'support', 'help', 'docs', 'status', 'cdn', 'assets', 'static', 'media',
    'test', 'staging', 'dev', 'demo', 'sandbox', 'localhost'
  ];
  return reserved.includes(subdomain.toLowerCase());
}
