{
  "name": "Shoe Repair POS Development",
  "dockerComposeFile": "docker-compose.yml",
  "service": "app",
  "workspaceFolder": "/workspace",
  
  // Features to install in the container
  "features": {
    "ghcr.io/devcontainers/features/node:1": {
      "nodeGypDependencies": true,
      "version": "20"
    },
    "ghcr.io/devcontainers/features/git:1": {
      "ppa": true,
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {
      "installDirectlyFromGitHubRelease": true,
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "moby": true,
      "azureDnsAutoDetection": true,
      "installDockerBuildx": true,
      "version": "latest"
    }
  },

  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "extensions": [
        // TypeScript & JavaScript
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        
        // React & Frontend
        "ms-vscode.vscode-react-native",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",
        
        // Database
        "alexcvzz.vscode-sqlite",
        "mtxr.sqltools",
        "mtxr.sqltools-driver-sqlite",
        
        // Git & GitHub
        "github.vscode-pull-request-github",
        "github.copilot",
        "github.copilot-chat",
        
        // Development Tools
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.vscode-npm-script",
        "christian-kohler.npm-intellisense",
        
        // API Development
        "humao.rest-client",
        "rangav.vscode-thunder-client",
        
        // Productivity
        "ms-vscode.vscode-todo-highlight",
        "gruntfuggly.todo-tree",
        "streetsidesoftware.code-spell-checker",
        "ms-vsliveshare.vsliveshare"
      ],
      
      "settings": {
        "typescript.preferences.importModuleSpecifier": "relative",
        "typescript.suggest.autoImports": true,
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit"
        },
        "files.associations": {
          "*.css": "tailwindcss"
        },
        "emmet.includeLanguages": {
          "javascript": "javascriptreact",
          "typescript": "typescriptreact"
        },
        "tailwindCSS.experimental.classRegex": [
          ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
          ["classnames\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
          ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
        ]
      }
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally
  "forwardPorts": [
    3001,  // Backend server
    5174,  // Frontend dev server (Vite)
    5173   // Alternative Vite port
  ],

  "portsAttributes": {
    "3001": {
      "label": "Backend API",
      "onAutoForward": "notify"
    },
    "5174": {
      "label": "Frontend Dev Server",
      "onAutoForward": "openBrowser"
    },
    "5173": {
      "label": "Frontend Dev Server (Alt)",
      "onAutoForward": "openBrowser"
    }
  },

  // Use 'postCreateCommand' to run commands after the container is created
  "postCreateCommand": "npm install && cd server && npm install",

  // Use 'postStartCommand' to run commands after the container starts
  "postStartCommand": {
    "server": "echo 'Container started! Run npm run dev to start the development servers.'"
  },

  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root
  "remoteUser": "node",

  // Set container environment variables
  "containerEnv": {
    "NODE_ENV": "development",
    "VITE_API_BASE_URL": "http://localhost:3001"
  },

  // Mount the local file system
  "mounts": [
    "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached"
  ]
}
