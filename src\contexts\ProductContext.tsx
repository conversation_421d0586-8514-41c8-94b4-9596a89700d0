import React, { createContext, useContext, useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import axios from 'axios';

export interface Product {
  id: string;
  name: string;
  price: number;
  description: string;
  imageUrl: string;
  categoryId: string;
  inStock: boolean;
  featured: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  productCount?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

interface ProductContextType {
  products: Product[];
  categories: Category[];
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  addCategory: (category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateCategory: (id: string, category: Partial<Category>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  getProductsByCategory: (categoryId: string) => Product[];
  getCategoryById: (id: string) => Category | undefined;
  isLoading: boolean;
  error: string | null;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

const API_BASE_URL = 'http://localhost:3001/api';

export const ProductProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const [categoriesRes, productsRes] = await Promise.all([
          axios.get(`${API_BASE_URL}/categories`).catch(err => {
            console.error('Failed to fetch categories:', err);
            throw new Error('Failed to fetch categories. Please try again later.');
          }),
          axios.get(`${API_BASE_URL}/products`).catch(err => {
            console.error('Failed to fetch products:', err);
            throw new Error('Failed to fetch products. Please try again later.');
          })
        ]);
        
        setCategories(categoriesRes.data);
        setProducts(productsRes.data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
        setError(errorMessage);
        toast.error(errorMessage);
        // Set empty arrays to prevent undefined errors
        setCategories([]);
        setProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const addProduct = async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/products`, product);
      const newProduct = { ...product, id: response.data.id };
      setProducts(prev => [...prev, newProduct]);
      toast.success('Product added successfully');
    } catch (err) {
      toast.error('Failed to add product');
      throw err;
    }
  };

  const updateProduct = async (id: string, product: Partial<Product>) => {
    try {
      await axios.put(`${API_BASE_URL}/products/${id}`, product);
      setProducts(prev =>
        prev.map(p => (p.id === id ? { ...p, ...product } : p))
      );
      toast.success('Product updated successfully');
    } catch (err) {
      toast.error('Failed to update product');
      throw err;
    }
  };

  const deleteProduct = async (id: string) => {
    try {
      await axios.delete(`${API_BASE_URL}/products/${id}`);
      setProducts(prev => prev.filter(p => p.id !== id));
      toast.success('Product deleted successfully');
    } catch (err) {
      toast.error('Failed to delete product');
      throw err;
    }
  };

  const addCategory = async (category: Omit<Category, 'id' | 'productCount' | 'createdAt' | 'updatedAt'>) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/categories`, category);
      const newCategory = { ...category, id: response.data.id, productCount: 0 };
      setCategories(prev => [...prev, newCategory]);
      toast.success('Category added successfully');
    } catch (err) {
      toast.error('Failed to add category');
      throw err;
    }
  };

  const updateCategory = async (id: string, category: Partial<Category>) => {
    try {
      await axios.put(`${API_BASE_URL}/categories/${id}`, category);
      setCategories(prev =>
        prev.map(c => (c.id === id ? { ...c, ...category } : c))
      );
      toast.success('Category updated successfully');
    } catch (err) {
      toast.error('Failed to update category');
      throw err;
    }
  };

  const deleteCategory = async (id: string) => {
    try {
      await axios.delete(`${API_BASE_URL}/categories/${id}`);
      setCategories(prev => prev.filter(c => c.id !== id));
      toast.success('Category deleted successfully');
    } catch (err) {
      toast.error('Failed to delete category');
      throw err;
    }
  };

  const getProductsByCategory = (categoryId: string) => {
    return products.filter(product => product.categoryId === categoryId);
  };

  const getCategoryById = (id: string) => {
    return categories.find(category => category.id === id);
  };

  return (
    <ProductContext.Provider
      value={{
        products,
        categories,
        addProduct,
        updateProduct,
        deleteProduct,
        addCategory,
        updateCategory,
        deleteCategory,
        getProductsByCategory,
        getCategoryById,
        isLoading,
        error
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};
