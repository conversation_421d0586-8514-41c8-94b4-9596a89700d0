import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTenant } from '../contexts/TenantContext';
import { UserRole } from '../contexts/AuthContext';
import { TenantUserRole } from '../types/tenant';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[] | TenantUserRole[];
  requiredPermissions?: string[];
  requiredFeatures?: string[];
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requiredFeatures = [],
  fallbackPath = '/login'
}) => {
  const { currentUser, userData, loading } = useAuth();
  const { tenantUser, tenant, hasPermission, hasRole, hasFeature, isInitialized } = useTenant();
  const location = useLocation();

  // Show loading while auth or tenant context is loading
  if (loading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-900 to-gray-800">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
      </div>
    );
  }

  // Check authentication - prefer tenant user over legacy auth
  const isAuthenticated = tenantUser || currentUser;
  const userRole = tenantUser?.role || userData?.role;

  if (!isAuthenticated) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check if tenant user account is active
  if (tenantUser && tenantUser.status !== 'active') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-yellow-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Account Inactive</h2>
          <p className="text-gray-600">Your account is currently inactive. Please contact your administrator.</p>
        </div>
      </div>
    );
  }

  // Check required roles
  if (requiredRoles.length > 0) {
    let hasRequiredRole = false;

    if (tenantUser) {
      // Use tenant role checking
      hasRequiredRole = hasRole(requiredRoles as TenantUserRole[]);
    } else if (userData) {
      // Fallback to legacy role checking
      hasRequiredRole = requiredRoles.includes(userData.role as any);
    }

    if (!hasRequiredRole) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">You don't have the required role to access this page.</p>
            <div className="text-sm text-gray-500">
              <p>Required roles: {requiredRoles.join(', ')}</p>
              <p>Your role: {userRole}</p>
            </div>
          </div>
        </div>
      );
    }
  }

  // Check required permissions (only for tenant users)
  if (requiredPermissions.length > 0 && tenantUser) {
    const hasAllPermissions = requiredPermissions.every(permission => hasPermission(permission));
    if (!hasAllPermissions) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">You don't have permission to access this page.</p>
            <div className="text-sm text-gray-500">
              <p>Required permissions: {requiredPermissions.join(', ')}</p>
              <p>Your role: {tenantUser.role}</p>
            </div>
          </div>
        </div>
      );
    }
  }

  // Check required features (only for tenant users)
  if (requiredFeatures.length > 0 && tenant) {
    const missingFeatures = requiredFeatures.filter(feature => !hasFeature(feature as any));
    if (missingFeatures.length > 0) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-blue-600 mb-4">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Feature Not Available</h2>
            <p className="text-gray-600 mb-4">This feature is not available in your current plan.</p>
            <div className="text-sm text-gray-500 mb-4">
              <p>Missing features: {missingFeatures.join(', ')}</p>
              <p>Current plan: {tenant?.plan_type}</p>
            </div>
            <button
              onClick={() => window.location.href = '/settings/billing'}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Upgrade Plan
            </button>
          </div>
        </div>
      );
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
