// Frontend tenant types and interfaces

export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  plan_type: TenantPlan;
  status: TenantStatus;
  settings: TenantSettings;
  created_at: string;
  updated_at?: string;
}

export type TenantPlan = 'trial' | 'basic' | 'professional' | 'enterprise';

export type TenantStatus = 'active' | 'suspended' | 'inactive' | 'pending';

export interface TenantSettings {
  businessInfo: {
    name: string;
    address?: string;
    phone?: string;
    email?: string;
    website?: string;
    logo_url?: string;
  };
  features: {
    inventory: boolean;
    reporting: boolean;
    multiLocation: boolean;
    customBranding: boolean;
    apiAccess: boolean;
    advancedAnalytics: boolean;
  };
  preferences: {
    timezone: string;
    currency: string;
    dateFormat: string;
    language: string;
    theme: 'light' | 'dark' | 'auto';
  };
  integrations: {
    payment: string[];
    shipping: string[];
    accounting: string[];
    notifications: {
      email: boolean;
      sms: boolean;
      whatsapp: boolean;
    };
  };
  limits: {
    maxUsers: number;
    maxOperations: number;
    storageLimitMB: number;
  };
}

export interface TenantUser {
  id: string;
  tenant_id: string;
  email: string;
  role: TenantUserRole;
  permissions: string[];
  status: 'active' | 'inactive' | 'pending';
  profile: {
    first_name?: string;
    last_name?: string;
    phone?: string;
    avatar_url?: string;
  };
  created_at: string;
  updated_at?: string;
  last_login?: string;
  tenant?: Tenant; // Populated tenant info
}

export type TenantUserRole = 'owner' | 'admin' | 'manager' | 'staff' | 'viewer';

export interface TenantContextType {
  // Current tenant state
  tenant: Tenant | null;
  tenantUser: TenantUser | null;
  isMultiTenant: boolean;
  
  // Loading states
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  switchTenant: (subdomain: string) => Promise<void>;
  refreshTenant: () => Promise<void>;
  updateTenantSettings: (settings: Partial<TenantSettings>) => Promise<void>;
  
  // Utilities
  hasFeature: (feature: keyof TenantSettings['features']) => boolean;
  hasPermission: (permission: string) => boolean;
  hasRole: (roles: TenantUserRole | TenantUserRole[]) => boolean;
  
  // Error handling
  error: string | null;
  clearError: () => void;
}

export interface TenantOnboardingData {
  name: string;
  subdomain: string;
  owner_email: string;
  owner_name?: string;
  plan_type?: TenantPlan;
}

export interface SubdomainInfo {
  subdomain: string | null;
  isValid: boolean;
  isReserved: boolean;
  isAvailable: boolean;
  tenant: Tenant | null;
}

// Plan feature definitions
export const PLAN_FEATURES: Record<TenantPlan, TenantSettings['features']> = {
  trial: {
    inventory: true,
    reporting: false,
    multiLocation: false,
    customBranding: false,
    apiAccess: false,
    advancedAnalytics: false
  },
  basic: {
    inventory: true,
    reporting: true,
    multiLocation: false,
    customBranding: false,
    apiAccess: false,
    advancedAnalytics: false
  },
  professional: {
    inventory: true,
    reporting: true,
    multiLocation: true,
    customBranding: true,
    apiAccess: true,
    advancedAnalytics: false
  },
  enterprise: {
    inventory: true,
    reporting: true,
    multiLocation: true,
    customBranding: true,
    apiAccess: true,
    advancedAnalytics: true
  }
};

// Plan limits
export const PLAN_LIMITS: Record<TenantPlan, TenantSettings['limits']> = {
  trial: {
    maxUsers: 2,
    maxOperations: 50,
    storageLimitMB: 10
  },
  basic: {
    maxUsers: 5,
    maxOperations: 1000,
    storageLimitMB: 100
  },
  professional: {
    maxUsers: 25,
    maxOperations: 10000,
    storageLimitMB: 1000
  },
  enterprise: {
    maxUsers: -1, // unlimited
    maxOperations: -1, // unlimited
    storageLimitMB: -1 // unlimited
  }
};

// Role permissions
export const ROLE_PERMISSIONS: Record<TenantUserRole, string[]> = {
  owner: ['*'], // All permissions
  admin: [
    'tenant.manage',
    'users.manage',
    'settings.manage',
    'operations.manage',
    'customers.manage',
    'inventory.manage',
    'reports.view',
    'billing.view'
  ],
  manager: [
    'operations.manage',
    'customers.manage',
    'inventory.manage',
    'reports.view'
  ],
  staff: [
    'operations.create',
    'operations.update',
    'customers.create',
    'customers.update',
    'inventory.view'
  ],
  viewer: [
    'operations.view',
    'customers.view',
    'inventory.view'
  ]
};

// Utility functions
export function validateSubdomain(subdomain: string): boolean {
  const regex = /^[a-z0-9]([a-z0-9-]{1,61}[a-z0-9])?$/;
  return regex.test(subdomain.toLowerCase());
}

export function isSubdomainReserved(subdomain: string): boolean {
  const reserved = [
    'www', 'api', 'app', 'admin', 'dashboard', 'mail', 'email', 'ftp', 'blog',
    'support', 'help', 'docs', 'status', 'cdn', 'assets', 'static', 'media',
    'test', 'staging', 'dev', 'demo', 'sandbox', 'localhost'
  ];
  return reserved.includes(subdomain.toLowerCase());
}

export function extractSubdomain(hostname: string): string | null {
  // Remove port if present
  const host = hostname.split(':')[0];
  
  // Split by dots
  const parts = host.split('.');
  
  // For localhost development, check for specific patterns
  if (host.includes('localhost') || host.includes('127.0.0.1')) {
    // Look for subdomain in format: subdomain.localhost:port
    if (parts.length >= 2 && parts[0] !== 'localhost') {
      return parts[0];
    }
    return null;
  }

  // For production domains, extract subdomain
  if (parts.length >= 3) {
    return parts[0];
  }

  return null;
}

export function hasPermission(userPermissions: string[], requiredPermission: string): boolean {
  // Owner has all permissions
  if (userPermissions.includes('*')) {
    return true;
  }
  
  // Check exact permission
  if (userPermissions.includes(requiredPermission)) {
    return true;
  }
  
  // Check wildcard permissions
  const permissionParts = requiredPermission.split('.');
  for (let i = permissionParts.length - 1; i > 0; i--) {
    const wildcardPermission = permissionParts.slice(0, i).join('.') + '.*';
    if (userPermissions.includes(wildcardPermission)) {
      return true;
    }
  }
  
  return false;
}

export function getUserPermissions(role: TenantUserRole): string[] {
  return ROLE_PERMISSIONS[role] || [];
}

export function canAccessFeature(tenantFeatures: TenantSettings['features'], feature: keyof TenantSettings['features']): boolean {
  return tenantFeatures[feature] === true;
}
