import React from 'react';
import { Tenant } from '../types/tenant';

interface TenantSuspendedProps {
  tenant: Tenant | null;
}

const TenantSuspended: React.FC<TenantSuspendedProps> = ({ tenant }) => {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="mx-auto h-24 w-24 text-red-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-3xl font-extrabold text-gray-900">
            Account Suspended
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {tenant ? `The account for "${tenant.name}" has been suspended.` : 'This account has been suspended.'}
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Account Status
              </h3>
              
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-medium text-red-800">
                        Suspended
                      </h4>
                      <p className="text-sm text-red-700 mt-1">
                        Your account access has been temporarily suspended.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="text-left space-y-3">
                  <h4 className="font-medium text-gray-900">Common reasons for suspension:</h4>
                  <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                    <li>Payment issues or overdue invoices</li>
                    <li>Terms of service violations</li>
                    <li>Security concerns</li>
                    <li>Account verification required</li>
                  </ul>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">What you can do:</h4>
                  <div className="space-y-3">
                    <a
                      href="mailto:<EMAIL>"
                      className="block w-full text-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Contact Billing
                    </a>
                    
                    <a
                      href="mailto:<EMAIL>"
                      className="block w-full text-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Contact Support
                    </a>
                  </div>
                </div>

                {tenant && (
                  <div className="bg-gray-50 rounded-lg p-3 text-left">
                    <h5 className="text-sm font-medium text-gray-900 mb-1">Account Details:</h5>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p><span className="font-medium">Store:</span> {tenant.name}</p>
                      <p><span className="font-medium">Subdomain:</span> {tenant.subdomain}</p>
                      <p><span className="font-medium">Plan:</span> {tenant.plan_type}</p>
                      {tenant.owner_email && (
                        <p><span className="font-medium">Owner:</span> {tenant.owner_email}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center">
        <p className="text-xs text-gray-500">
          Shoe Repair POS - Professional Shoe Repair Management
        </p>
      </div>
    </div>
  );
};

export default TenantSuspended;
