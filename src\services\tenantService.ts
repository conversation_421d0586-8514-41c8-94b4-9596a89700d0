import { 
  Tenant, 
  TenantUser, 
  TenantOnboardingData, 
  TenantSettings,
  SubdomainInfo 
} from '../types/tenant';

class TenantService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
  }

  // Tenant Management
  async getCurrentTenant(): Promise<Tenant | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/current`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 404) {
        return null; // No tenant found
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch tenant: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current tenant:', error);
      throw error;
    }
  }

  async updateTenant(updates: Partial<TenantSettings>): Promise<Tenant> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/current`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update tenant');
      }

      const result = await response.json();
      return result.tenant;
    } catch (error) {
      console.error('Error updating tenant:', error);
      throw error;
    }
  }

  async createTenant(data: TenantOnboardingData): Promise<{ tenant: Tenant; user: TenantUser }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create tenant');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating tenant:', error);
      throw error;
    }
  }

  // User Management
  async getCurrentUser(): Promise<TenantUser | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/me`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 401 || response.status === 404) {
        return null; // No user authenticated
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch user: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current user:', error);
      throw error;
    }
  }

  async getTenantUsers(): Promise<TenantUser[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/users`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch tenant users: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching tenant users:', error);
      throw error;
    }
  }

  // Subdomain Management
  async checkSubdomainAvailability(subdomain: string): Promise<{ available: boolean; reason?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/check-subdomain/${subdomain}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to check subdomain: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error checking subdomain availability:', error);
      throw error;
    }
  }

  // Health and Stats
  async getTenantHealth(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tenants/health`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch tenant health: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching tenant health:', error);
      throw error;
    }
  }

  // Utility Methods
  getSubdomainInfo(hostname: string = window.location.hostname): SubdomainInfo {
    const subdomain = this.extractSubdomain(hostname);
    
    return {
      subdomain,
      isValid: subdomain ? this.validateSubdomain(subdomain) : false,
      isReserved: subdomain ? this.isSubdomainReserved(subdomain) : false,
      isAvailable: false, // Will be checked via API
      tenant: null // Will be populated when tenant is loaded
    };
  }

  private extractSubdomain(hostname: string): string | null {
    // Remove port if present
    const host = hostname.split(':')[0];
    
    // Split by dots
    const parts = host.split('.');
    
    // For localhost development, check for specific patterns
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      // Look for subdomain in format: subdomain.localhost:port
      if (parts.length >= 2 && parts[0] !== 'localhost') {
        return parts[0];
      }
      return null;
    }

    // For production domains, extract subdomain
    if (parts.length >= 3) {
      return parts[0];
    }

    return null;
  }

  private validateSubdomain(subdomain: string): boolean {
    const regex = /^[a-z0-9]([a-z0-9-]{1,61}[a-z0-9])?$/;
    return regex.test(subdomain.toLowerCase());
  }

  private isSubdomainReserved(subdomain: string): boolean {
    const reserved = [
      'www', 'api', 'app', 'admin', 'dashboard', 'mail', 'email', 'ftp', 'blog',
      'support', 'help', 'docs', 'status', 'cdn', 'assets', 'static', 'media',
      'test', 'staging', 'dev', 'demo', 'sandbox', 'localhost'
    ];
    return reserved.includes(subdomain.toLowerCase());
  }

  // Navigation helpers
  buildTenantUrl(subdomain: string, path: string = ''): string {
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;
    
    // For localhost development
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
      const portSuffix = port ? `:${port}` : '';
      return `${protocol}//${subdomain}.localhost${portSuffix}${path}`;
    }
    
    // For production
    const baseDomain = hostname.split('.').slice(-2).join('.');
    const portSuffix = port ? `:${port}` : '';
    return `${protocol}//${subdomain}.${baseDomain}${portSuffix}${path}`;
  }

  redirectToTenant(subdomain: string, path: string = ''): void {
    const url = this.buildTenantUrl(subdomain, path);
    window.location.href = url;
  }

  // Error handling
  isMultiTenantError(error: any): boolean {
    return error?.code && [
      'TENANT_NOT_FOUND',
      'TENANT_INACTIVE',
      'SUBDOMAIN_REQUIRED',
      'TENANT_RESOLUTION_ERROR'
    ].includes(error.code);
  }

  handleMultiTenantError(error: any): void {
    if (error?.code === 'TENANT_NOT_FOUND') {
      // Redirect to tenant not found page
      window.location.href = '/tenant-not-found';
    } else if (error?.code === 'TENANT_INACTIVE') {
      // Redirect to tenant suspended page
      window.location.href = '/tenant-suspended';
    } else if (error?.code === 'SUBDOMAIN_REQUIRED') {
      // Redirect to main landing page
      window.location.href = '/';
    }
  }
}

export const tenantService = new TenantService();
export default tenantService;
