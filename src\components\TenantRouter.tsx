import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useTenant } from '../contexts/TenantContext';

// Import existing components
import { Dashboard } from './Dashboard';
import { Login } from './Login';
import StorePage from '../pages/StorePage';
import CustomerPage from '../pages/CustomerPage';
import DropPage from '../pages/DropPage';
import PickupPage from '../pages/PickupPage';
import OperationPage from '../pages/OperationPage';
import SalesPage from '../pages/SalesPage';

// Import new tenant-specific components
import TenantNotFound from './TenantNotFound';
import TenantSuspended from './TenantSuspended';
import TenantOnboarding from './TenantOnboarding';
import TenantSettings from './TenantSettings';
import ProtectedRoute from './ProtectedRoute';

interface TenantRouterProps {
  onNewOrder?: () => void;
}

const TenantRouter: React.FC<TenantRouterProps> = ({ onNewOrder }) => {
  const { tenant, tenantUser, isMultiTenant, isLoading, error } = useTenant();

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading application...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Application Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Multi-tenant mode without tenant
  if (isMultiTenant && !tenant) {
    return <TenantNotFound />;
  }

  // Tenant suspended
  if (tenant && tenant.status === 'suspended') {
    return <TenantSuspended tenant={tenant} />;
  }

  // Tenant inactive
  if (tenant && tenant.status === 'inactive') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Tenant Inactive</h2>
          <p className="text-gray-600">This tenant account is currently inactive.</p>
        </div>
      </div>
    );
  }

  // Main application routes
  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<Login />} />
      <Route path="/tenant-not-found" element={<TenantNotFound />} />
      <Route path="/tenant-suspended" element={<TenantSuspended tenant={tenant} />} />
      <Route path="/onboarding" element={<TenantOnboarding />} />

      {/* Protected routes - require authentication */}
      <Route path="/" element={
        <ProtectedRoute>
          <Dashboard onNewOrder={onNewOrder || (() => {})} />
        </ProtectedRoute>
      } />

      <Route path="/store" element={
        <ProtectedRoute>
          <StorePage />
        </ProtectedRoute>
      } />

      <Route path="/customers" element={
        <ProtectedRoute>
          <CustomerPage />
        </ProtectedRoute>
      } />

      <Route path="/drop" element={
        <ProtectedRoute>
          <DropPage />
        </ProtectedRoute>
      } />

      <Route path="/pickup" element={
        <ProtectedRoute>
          <PickupPage />
        </ProtectedRoute>
      } />

      <Route path="/operations" element={
        <ProtectedRoute>
          <OperationPage />
        </ProtectedRoute>
      } />

      <Route path="/sales" element={
        <ProtectedRoute>
          <SalesPage />
        </ProtectedRoute>
      } />

      {/* Admin routes - require admin permissions */}
      <Route path="/settings" element={
        <ProtectedRoute requiredPermissions={['tenant.manage', 'settings.manage']}>
          <TenantSettings />
        </ProtectedRoute>
      } />

      <Route path="/settings/users" element={
        <ProtectedRoute requiredPermissions={['users.manage']}>
          <div>User Management (Coming Soon)</div>
        </ProtectedRoute>
      } />

      <Route path="/settings/billing" element={
        <ProtectedRoute requiredPermissions={['billing.view']} requiredRoles={['owner', 'admin']}>
          <div>Billing Management (Coming Soon)</div>
        </ProtectedRoute>
      } />

      {/* Feature-gated routes */}
      <Route path="/reports" element={
        <ProtectedRoute requiredFeatures={['reporting']}>
          <div>Reports (Coming Soon)</div>
        </ProtectedRoute>
      } />

      <Route path="/analytics" element={
        <ProtectedRoute requiredFeatures={['advancedAnalytics']}>
          <div>Advanced Analytics (Coming Soon)</div>
        </ProtectedRoute>
      } />

      <Route path="/api-docs" element={
        <ProtectedRoute requiredFeatures={['apiAccess']}>
          <div>API Documentation (Coming Soon)</div>
        </ProtectedRoute>
      } />

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default TenantRouter;
