import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { 
  Tenant, 
  TenantUser, 
  TenantContextType, 
  TenantSettings,
  TenantUserRole,
  hasPermission,
  getUserPermissions,
  canAccessFeature
} from '../types/tenant';
import tenantService from '../services/tenantService';

const TenantContext = createContext<TenantContextType | undefined>(undefined);

interface TenantProviderProps {
  children: React.ReactNode;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ children }) => {
  // State
  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [tenantUser, setTenantUser] = useState<TenantUser | null>(null);
  const [isMultiTenant, setIsMultiTenant] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize tenant context
  const initializeTenant = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if multi-tenancy is enabled
      const health = await tenantService.getTenantHealth();
      setIsMultiTenant(health.multiTenantEnabled || false);

      // Get subdomain info
      const subdomainInfo = tenantService.getSubdomainInfo();
      
      // If multi-tenancy is disabled, we might still have a default tenant
      if (!isMultiTenant && !subdomainInfo.subdomain) {
        // Try to get current tenant (might be default tenant)
        try {
          const currentTenant = await tenantService.getCurrentTenant();
          if (currentTenant) {
            setTenant(currentTenant);
          }
        } catch (err) {
          // No tenant found, that's okay for single-tenant mode
          console.log('No tenant found, running in single-tenant mode');
        }
      } else if (isMultiTenant) {
        // Multi-tenancy is enabled, we need a valid subdomain
        if (!subdomainInfo.subdomain) {
          throw new Error('Subdomain required for multi-tenant mode');
        }

        if (!subdomainInfo.isValid) {
          throw new Error('Invalid subdomain format');
        }

        // Load tenant by subdomain
        const currentTenant = await tenantService.getCurrentTenant();
        if (!currentTenant) {
          throw new Error('Tenant not found');
        }

        setTenant(currentTenant);
      }

      // Load current user if we have a tenant
      if (tenant || !isMultiTenant) {
        try {
          const currentUser = await tenantService.getCurrentUser();
          setTenantUser(currentUser);
        } catch (err) {
          // User not authenticated, that's okay
          console.log('No authenticated user found');
        }
      }

      setIsInitialized(true);
    } catch (err: any) {
      console.error('Failed to initialize tenant context:', err);
      setError(err.message || 'Failed to initialize tenant');
      
      // Handle multi-tenant specific errors
      if (tenantService.isMultiTenantError(err)) {
        tenantService.handleMultiTenantError(err);
      }
    } finally {
      setIsLoading(false);
    }
  }, [tenant, isMultiTenant]);

  // Initialize on mount
  useEffect(() => {
    initializeTenant();
  }, []);

  // Actions
  const switchTenant = useCallback(async (subdomain: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Redirect to the new tenant subdomain
      tenantService.redirectToTenant(subdomain);
    } catch (err: any) {
      console.error('Failed to switch tenant:', err);
      setError(err.message || 'Failed to switch tenant');
      setIsLoading(false);
    }
  }, []);

  const refreshTenant = useCallback(async () => {
    await initializeTenant();
  }, [initializeTenant]);

  const updateTenantSettings = useCallback(async (settings: Partial<TenantSettings>) => {
    if (!tenant) {
      throw new Error('No tenant to update');
    }

    try {
      setIsLoading(true);
      setError(null);

      const updatedTenant = await tenantService.updateTenant(settings);
      setTenant(updatedTenant);
    } catch (err: any) {
      console.error('Failed to update tenant settings:', err);
      setError(err.message || 'Failed to update tenant settings');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [tenant]);

  // Utility functions
  const hasFeature = useCallback((feature: keyof TenantSettings['features']): boolean => {
    if (!tenant?.settings?.features) {
      return false;
    }
    return canAccessFeature(tenant.settings.features, feature);
  }, [tenant]);

  const hasPermissionCheck = useCallback((permission: string): boolean => {
    if (!tenantUser) {
      return false;
    }

    const userPermissions = tenantUser.permissions.length > 0 
      ? tenantUser.permissions 
      : getUserPermissions(tenantUser.role);

    return hasPermission(userPermissions, permission);
  }, [tenantUser]);

  const hasRole = useCallback((roles: TenantUserRole | TenantUserRole[]): boolean => {
    if (!tenantUser) {
      return false;
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    return allowedRoles.includes(tenantUser.role);
  }, [tenantUser]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Context value
  const contextValue: TenantContextType = {
    // State
    tenant,
    tenantUser,
    isMultiTenant,
    isLoading,
    isInitialized,
    error,

    // Actions
    switchTenant,
    refreshTenant,
    updateTenantSettings,

    // Utilities
    hasFeature,
    hasPermission: hasPermissionCheck,
    hasRole,
    clearError,
  };

  return (
    <TenantContext.Provider value={contextValue}>
      {children}
    </TenantContext.Provider>
  );
};

// Hook to use tenant context
export const useTenant = (): TenantContextType => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

// HOC for components that require tenant context
export function withTenant<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function TenantWrappedComponent(props: P) {
    const tenantContext = useTenant();
    
    if (!tenantContext.isInitialized) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading tenant...</p>
          </div>
        </div>
      );
    }

    if (tenantContext.error) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Tenant Error</h2>
            <p className="text-gray-600 mb-4">{tenantContext.error}</p>
            <button
              onClick={tenantContext.refreshTenant}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return <Component {...props} />;
  };
}

export default TenantContext;
