import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  CreditCard, 
  Search, 
  Filter, 
  Download, 
  Phone, 
  Calendar, 
  RefreshCw,
  DollarSign,
  User,
  AlertCircle
} from 'lucide-react';
import { Link } from 'react-router-dom';
import type { RepairItem } from '../types';
import { mockPickups } from '../data/mockData';
import { format, parseISO } from 'date-fns';

// In a real app, you would fetch this data from your API
const fetchCreditItems = (): Promise<RepairItem[]> => {
  return new Promise((resolve) => {
    // Simulate API call delay
    setTimeout(() => {
      // Filter items with balance due (credit)
      const creditItems = mockPickups.filter(item => 
        item.balanceDue && item.balanceDue > 0
      );
      
      resolve(creditItems);
    }, 800);
  });
};

interface CreditStats {
  totalItems: number;
  totalCredit: number;
  averageCredit: number;
  highestCredit: number;
}

const CreditListPage: React.FC = () => {
  const [items, setItems] = useState<RepairItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<RepairItem[]>([]);
  const [stats, setStats] = useState<CreditStats>({
    totalItems: 0,
    totalCredit: 0,
    averageCredit: 0,
    highestCredit: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [amountFilter, setAmountFilter] = useState<string>('all');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await fetchCreditItems();
        setItems(data);
        
        // Calculate stats
        if (data.length > 0) {
          const totalCredit = data.reduce((sum, item) => sum + (item.balanceDue || 0), 0);
          const highestCredit = Math.max(...data.map(item => item.balanceDue || 0));
          
          setStats({
            totalItems: data.length,
            totalCredit: totalCredit,
            averageCredit: totalCredit / data.length,
            highestCredit: highestCredit
          });
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [refreshKey]);

  // Filter items based on search query and amount filter
  useEffect(() => {
    let result = items;
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item => 
        item.customerName.toLowerCase().includes(query) || 
        item.description.toLowerCase().includes(query) ||
        item.contactNumber.includes(query)
      );
    }
    
    // Apply amount filter
    if (amountFilter !== 'all') {
      if (amountFilter === 'low') {
        result = result.filter(item => (item.balanceDue || 0) < 50000);
      } else if (amountFilter === 'medium') {
        result = result.filter(item => 
          (item.balanceDue || 0) >= 50000 && (item.balanceDue || 0) < 100000
        );
      } else if (amountFilter === 'high') {
        result = result.filter(item => (item.balanceDue || 0) >= 100000);
      }
    }
    
    setFilteredItems(result);
  }, [items, searchQuery, amountFilter]);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleExport = () => {
    // In a real app, you would implement CSV export functionality
    alert('Export functionality would be implemented here');
  };

  const handleCollectPayment = (id: string) => {
    // In a real app, you would open a payment collection dialog
    alert(`Collect payment for item #${id}`);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-UG', {
      style: 'currency',
      currency: 'UGX',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <Link to="/" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeft size={24} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Credit List</h1>
            <p className="text-gray-500">Customers with outstanding balance</p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleRefresh}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <RefreshCw size={18} className="mr-2" />
            Refresh
          </button>
          
          <button
            onClick={handleExport}
            className="flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <Download size={18} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {/* Stats Cards */}
      {loading ? (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-purple-500">
            <p className="text-gray-500 text-sm mb-1">Total Credit Accounts</p>
            <p className="text-2xl font-bold text-purple-600">{stats.totalItems}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-red-500">
            <p className="text-gray-500 text-sm mb-1">Total Outstanding</p>
            <p className="text-2xl font-bold text-red-600">{formatCurrency(stats.totalCredit)}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-blue-500">
            <p className="text-gray-500 text-sm mb-1">Average Credit</p>
            <p className="text-2xl font-bold text-blue-600">{formatCurrency(stats.averageCredit)}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-orange-500">
            <p className="text-gray-500 text-sm mb-1">Highest Credit</p>
            <p className="text-2xl font-bold text-orange-600">{formatCurrency(stats.highestCredit)}</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by customer name or phone..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="w-full md:w-64">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
              value={amountFilter}
              onChange={(e) => setAmountFilter(e.target.value)}
            >
              <option value="all">All Amounts</option>
              <option value="low">Low (< 50,000 UGX)</option>
              <option value="medium">Medium (50,000 - 100,000 UGX)</option>
              <option value="high">High (> 100,000 UGX)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Items List */}
      {loading ? (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : filteredItems.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <CreditCard size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">No credit accounts found</h3>
          <p className="text-gray-500 mb-4">
            There are no customers with outstanding balances, or your search filters don't match any items.
          </p>
          <button
            onClick={() => {
              setSearchQuery('');
              setAmountFilter('all');
            }}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Clear Filters
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Balance Due
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ready Since
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <User size={20} className="text-gray-500" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{item.customerName}</div>
                          <div className="flex items-center text-sm text-gray-500">
                            <Phone size={14} className="mr-1" />
                            {item.contactNumber}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 capitalize">{item.type}</div>
                      <div className="text-sm text-gray-500">{item.description}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(item.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <AlertCircle size={16} className="text-red-500 mr-2" />
                        <span className="text-red-600 font-medium">
                          {formatCurrency(item.balanceDue || 0)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar size={14} className="mr-1" />
                        {format(parseISO(item.estimatedCompletion), 'MMM dd, yyyy')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        className="text-indigo-600 hover:text-indigo-900 mr-3"
                        onClick={() => alert(`View details for ${item.customerName}`)}
                      >
                        View
                      </button>
                      <button
                        className="text-green-600 hover:text-green-900"
                        onClick={() => handleCollectPayment(item.id)}
                      >
                        Collect
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreditListPage;
