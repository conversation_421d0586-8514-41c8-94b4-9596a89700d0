import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Chip, 
  IconButton, 
  Tooltip, 
  CircularProgress,
  Grid,
  Divider,
  Box,
  Alert,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import { 
  Timeline, 
  TimelineItem, 
  TimelineSeparator, 
  TimelineConnector, 
  TimelineContent, 
  TimelineDot 
} from '@mui/lab';
import { 
  AccessTime, 
  Person, 
  Edit, 
  Delete, 
  Add, 
  Refresh, 
  CheckCircle, 
  PendingActions,
  HourglassEmpty,
  LocalShipping,
  Search,
  FilterList,
  SortByAlpha,
  Download
} from '@mui/icons-material';
import { HoldItem, HoldStats, HoldFormData, HoldStatus } from '../types/holdTypes';
import { getHoldItems, getHoldStats, getTodayTimeline, createHoldItem, updateHoldItem, deleteHoldItem } from '../services/holdService';
import { formatDate, formatTime, formatShortDate, isToday } from '../utils/dateUtils';
import HoldItemForm from '../components/HoldItemForm';

const statusColors: Record<HoldStatus, string> = {
  'pending': 'bg-yellow-100 text-yellow-800',
  'in-progress': 'bg-blue-100 text-blue-800',
  'ready': 'bg-green-100 text-green-800',
  'completed': 'bg-gray-100 text-gray-800'
};

const statusIcons: Record<HoldStatus, React.ReactNode> = {
  'pending': <PendingActions fontSize="small" />,
  'in-progress': <HourglassEmpty fontSize="small" />,
  'ready': <CheckCircle fontSize="small" />,
  'completed': <LocalShipping fontSize="small" />
};

const HoldQuickDropPage: React.FC = () => {
  const [holdItems, setHoldItems] = useState<HoldItem[]>([]);
  const [timelineItems, setTimelineItems] = useState<HoldItem[]>([]);
  const [stats, setStats] = useState<HoldStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formOpen, setFormOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<HoldItem | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [items, todayTimeline, statsData] = await Promise.all([
          getHoldItems(),
          getTodayTimeline(),
          getHoldStats()
        ]);
        
        setHoldItems(items);
        setTimelineItems(todayTimeline);
        setStats(statsData);
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [refreshKey]);

  // Handle form submission
  const handleFormSubmit = async (data: HoldFormData) => {
    try {
      if (selectedItem) {
        await updateHoldItem(selectedItem.id, data);
      } else {
        await createHoldItem(data);
      }
      
      setFormOpen(false);
      setSelectedItem(null);
      setRefreshKey(prev => prev + 1); // Trigger a refresh
    } catch (err) {
      console.error('Error submitting form:', err);
      setError('Failed to save data. Please try again.');
    }
  };

  // Handle item deletion
  const handleDeleteItem = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        await deleteHoldItem(id);
        setRefreshKey(prev => prev + 1); // Trigger a refresh
      } catch (err) {
        console.error('Error deleting item:', err);
        setError('Failed to delete item. Please try again.');
      }
    }
  };

  // Filter items based on tab and search query
  const filteredItems = holdItems.filter(item => {
    const matchesSearch = searchQuery === '' || 
      item.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.item_description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (!matchesSearch) return false;
    
    switch (activeTab) {
      case 0: // All items
        return true;
      case 1: // Hold items
        return item.is_quick_drop === 0;
      case 2: // Quick drops
        return item.is_quick_drop === 1;
      case 3: // Due today
        return isToday(item.expected_completion);
      case 4: // Ready for pickup
        return item.status === 'ready';
      default:
        return true;
    }
  });

  // Render status chip
  const renderStatusChip = (status: HoldStatus) => (
    <Chip
      icon={statusIcons[status]}
      label={status.replace('-', ' ')}
      size="small"
      className={`${statusColors[status]} capitalize`}
    />
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <Typography variant="h4" className="text-gray-800 font-semibold">
            Hold & Quick Drop Management
          </Typography>
          <Typography variant="body1" className="text-gray-600 mt-1">
            Manage customer items on hold and same-day quick drop services
          </Typography>
        </div>
        <div className="flex flex-wrap gap-3">
          <Button 
            variant="contained" 
            color="primary"
            className="bg-blue-600 hover:bg-blue-700"
            startIcon={<Add />}
            onClick={() => {
              setSelectedItem(null);
              setFormOpen(true);
            }}
          >
            New Item
          </Button>
          <Button 
            variant="outlined"
            className="border-blue-600 text-blue-600 hover:bg-blue-50"
            startIcon={<Refresh />}
            onClick={() => setRefreshKey(prev => prev + 1)}
          >
            Refresh
          </Button>
          <Button 
            variant="outlined"
            className="border-green-600 text-green-600 hover:bg-green-50"
            startIcon={<Download />}
          >
            Export
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" className="mb-6" onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      {loading ? (
        <div className="flex justify-center my-8">
          <CircularProgress />
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
            <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 border-blue-500">
              <Typography variant="subtitle1" className="text-gray-600 mb-1">
                Items on Hold
              </Typography>
              <Typography variant="h3" className="text-blue-600 font-bold">
                {stats?.totalHoldItems || 0}
              </Typography>
            </Card>
            <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 border-purple-500">
              <Typography variant="subtitle1" className="text-gray-600 mb-1">
                Quick Drops
              </Typography>
              <Typography variant="h3" className="text-purple-600 font-bold">
                {stats?.totalQuickDrops || 0}
              </Typography>
            </Card>
            <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 border-orange-500">
              <Typography variant="subtitle1" className="text-gray-600 mb-1">
                Due Today
              </Typography>
              <Typography variant="h3" className="text-orange-600 font-bold">
                {stats?.dueToday || 0}
              </Typography>
            </Card>
            <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 border-yellow-500">
              <Typography variant="subtitle1" className="text-gray-600 mb-1">
                In Progress
              </Typography>
              <Typography variant="h3" className="text-yellow-600 font-bold">
                {stats?.inProgressItems || 0}
              </Typography>
            </Card>
            <Card className="p-4 shadow-md hover:shadow-lg transition-shadow duration-200 border-l-4 border-green-500">
              <Typography variant="subtitle1" className="text-gray-600 mb-1">
                Ready for Pickup
              </Typography>
              <Typography variant="h3" className="text-green-600 font-bold">
                {stats?.readyItems || 0}
              </Typography>
            </Card>
          </div>

          {/* Timeline Section */}
          {timelineItems.length > 0 && (
            <Card className="p-6 mb-6 shadow-md">
              <Typography variant="h6" className="mb-4 text-gray-800 flex items-center">
                <AccessTime className="mr-2" /> Today's Timeline
              </Typography>
              <Timeline position="alternate">
                {timelineItems.map((item) => (
                  <TimelineItem key={item.id}>
                    <TimelineSeparator>
                      <TimelineDot color={item.status === 'ready' ? "success" : item.is_quick_drop ? "secondary" : "primary"} />
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent>
                      <Card className="p-3 shadow-sm hover:shadow-md transition-shadow duration-200 border-l-4 border-blue-400">
                        <div className="flex justify-between items-start">
                          <div>
                            <Typography variant="subtitle1" className="font-medium">
                              {item.customer_name}
                            </Typography>
                            <Typography variant="body2" className="text-gray-600">
                              {item.item_description}
                            </Typography>
                            <div className="flex items-center mt-2 text-sm text-gray-500">
                              <AccessTime className="w-4 h-4 mr-1" />
                              <span>Expected: {formatTime(item.expected_completion)}</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            {renderStatusChip(item.status)}
                            {item.is_quick_drop === 1 && (
                              <Chip 
                                label="Quick Drop" 
                                size="small" 
                                className="bg-purple-100 text-purple-800 mt-2"
                              />
                            )}
                          </div>
                        </div>
                      </Card>
                    </TimelineContent>
                  </TimelineItem>
                ))}
              </Timeline>
            </Card>
          )}

          {/* Items List */}
          <Card className="shadow-md">
            <div className="p-4 bg-gray-50 border-b">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <Tabs 
                  value={activeTab} 
                  onChange={(_, newValue) => setActiveTab(newValue)}
                  variant="scrollable"
                  scrollButtons="auto"
                >
                  <Tab label="All Items" />
                  <Tab 
                    label={
                      <Badge badgeContent={stats?.totalHoldItems || 0} color="primary">
                        Hold Items
                      </Badge>
                    } 
                  />
                  <Tab 
                    label={
                      <Badge badgeContent={stats?.totalQuickDrops || 0} color="secondary">
                        Quick Drops
                      </Badge>
                    } 
                  />
                  <Tab 
                    label={
                      <Badge badgeContent={stats?.dueToday || 0} color="warning">
                        Due Today
                      </Badge>
                    } 
                  />
                  <Tab 
                    label={
                      <Badge badgeContent={stats?.readyItems || 0} color="success">
                        Ready
                      </Badge>
                    } 
                  />
                </Tabs>
                <div className="relative w-full md:w-64">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search items..."
                    className="w-full bg-white pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="p-4">
              {filteredItems.length === 0 ? (
                <div className="text-center py-8">
                  <Typography variant="h6" className="text-gray-500">
                    No items found
                  </Typography>
                  <Typography variant="body2" className="text-gray-400 mt-2">
                    {searchQuery ? 'Try a different search term' : 'Add a new item to get started'}
                  </Typography>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredItems.map((item) => (
                    <Card key={item.id} className="p-4 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <div className="flex items-start">
                            <Person className="text-gray-400 mr-3 mt-1" />
                            <div>
                              <Typography variant="subtitle1" className="font-medium">
                                {item.customer_name}
                                {item.is_quick_drop === 1 && (
                                  <Chip 
                                    label="Quick Drop" 
                                    size="small" 
                                    className="bg-purple-100 text-purple-800 ml-2"
                                  />
                                )}
                              </Typography>
                              <Typography variant="body2" className="text-gray-600">
                                {item.item_description}
                              </Typography>
                              {item.notes && (
                                <Typography variant="body2" className="text-gray-500 mt-1 italic">
                                  Note: {item.notes}
                                </Typography>
                              )}
                            </div>
                          </div>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <div className="flex flex-col h-full justify-center">
                            <div className="flex items-center text-sm text-gray-500 mb-1">
                              <AccessTime className="w-4 h-4 mr-1" />
                              <span>Hold since: {formatShortDate(item.hold_date)}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-500">
                              <AccessTime className="w-4 h-4 mr-1" />
                              <span>Expected: {formatDate(item.expected_completion)}</span>
                            </div>
                          </div>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <div className="flex justify-between items-center h-full">
                            <div>
                              {renderStatusChip(item.status)}
                            </div>
                            <div className="flex space-x-2">
                              <Tooltip title="Edit">
                                <IconButton 
                                  size="small" 
                                  className="text-blue-600"
                                  onClick={() => {
                                    setSelectedItem(item);
                                    setFormOpen(true);
                                  }}
                                >
                                  <Edit />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <IconButton 
                                  size="small" 
                                  className="text-red-600"
                                  onClick={() => handleDeleteItem(item.id)}
                                >
                                  <Delete />
                                </IconButton>
                              </Tooltip>
                            </div>
                          </div>
                        </Grid>
                      </Grid>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </>
      )}

      {/* Hold Item Form Dialog */}
      <HoldItemForm
        open={formOpen}
        onClose={() => {
          setFormOpen(false);
          setSelectedItem(null);
        }}
        onSubmit={handleFormSubmit}
        initialData={selectedItem || undefined}
        title={selectedItem ? 'Edit Hold Item' : 'Add New Hold Item'}
      />
    </div>
  );
};

export default HoldQuickDropPage;
