import express from 'express';
import { TenantService } from '../services/TenantService';
import { tenantMiddleware } from '../middleware/tenantMiddleware';
import { validateSubdomain, isSubdomainReserved } from '../types/tenant';
import bcrypt from 'bcrypt';

const router = express.Router();
const tenantService = new TenantService();

// Public routes (no tenant context required)

// Check subdomain availability
router.get('/check-subdomain/:subdomain', async (req, res) => {
  try {
    const { subdomain } = req.params;
    
    if (!validateSubdomain(subdomain)) {
      return res.json({
        available: false,
        reason: 'Invalid subdomain format'
      });
    }

    if (isSubdomainReserved(subdomain)) {
      return res.json({
        available: false,
        reason: 'Subdomain is reserved'
      });
    }

    const available = tenantService.isSubdomainAvailable(subdomain);
    
    res.json({
      available,
      reason: available ? null : 'Subdomain already exists'
    });
  } catch (error) {
    console.error('Error checking subdomain availability:', error);
    res.status(500).json({ error: 'Failed to check subdomain availability' });
  }
});

// Create new tenant (tenant onboarding)
router.post('/create', async (req, res) => {
  try {
    const { name, subdomain, owner_email, owner_name, plan_type, password } = req.body;

    // Validate required fields
    if (!name || !subdomain || !owner_email) {
      return res.status(400).json({ 
        error: 'Missing required fields: name, subdomain, owner_email' 
      });
    }

    // Create tenant
    const result = await tenantService.createTenant({
      name,
      subdomain,
      owner_email,
      owner_name,
      plan_type: plan_type || 'basic',
      password
    });

    res.status(201).json({
      message: 'Tenant created successfully',
      tenant: {
        id: result.tenant.id,
        name: result.tenant.name,
        subdomain: result.tenant.subdomain,
        plan_type: result.tenant.plan_type,
        status: result.tenant.status
      },
      user: {
        id: result.user.id,
        email: result.user.email,
        role: result.user.role
      }
    });
  } catch (error) {
    console.error('Error creating tenant:', error);
    res.status(400).json({ 
      error: error instanceof Error ? error.message : 'Failed to create tenant' 
    });
  }
});

// Tenant login endpoint (no tenant context required)
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }

    // Get subdomain from hostname
    const hostname = req.get('host') || '';
    const subdomain = extractSubdomain(hostname);

    if (!subdomain) {
      return res.status(400).json({ error: 'Tenant subdomain required' });
    }

    // Get tenant
    const tenant = tenantService.getTenantBySubdomain(subdomain);
    if (!tenant) {
      return res.status(404).json({ error: 'Tenant not found' });
    }

    // Get user
    const user = tenantService.getTenantUser(tenant.id, email);
    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password
    if (!user.password_hash) {
      return res.status(401).json({ error: 'Password not set for this user' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Update last login
    const masterDb = tenantService.getMasterDatabase();
    masterDb.prepare(`
      UPDATE tenant_users SET last_login = ? WHERE id = ?
    `).run(new Date().toISOString(), user.id);

    // Return user data (without password hash)
    res.json({
      message: 'Login successful',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        status: user.status,
        profile: user.profile,
        permissions: user.permissions,
        tenant: {
          id: tenant.id,
          name: tenant.name,
          subdomain: tenant.subdomain,
          plan_type: tenant.plan_type
        }
      }
    });
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Helper function to extract subdomain
function extractSubdomain(hostname: string): string | null {
  const host = hostname.split(':')[0];
  const parts = host.split('.');

  if (host.includes('localhost') || host.includes('127.0.0.1')) {
    if (parts.length >= 2 && parts[0] !== 'localhost') {
      return parts[0];
    }
    return null;
  }

  if (parts.length >= 3) {
    return parts[0];
  }

  return null;
}

// Routes that require tenant context
router.use(tenantMiddleware.resolveTenant);
router.use(tenantMiddleware.resolveTenantUser);
router.use(tenantMiddleware.requireTenant);

// Get current tenant information
router.get('/current', (req, res) => {
  try {
    if (!req.tenant) {
      return res.status(404).json({ error: 'Tenant not found' });
    }

    // Return tenant info without sensitive data
    const tenantInfo = {
      id: req.tenant.id,
      name: req.tenant.name,
      subdomain: req.tenant.subdomain,
      plan_type: req.tenant.plan_type,
      status: req.tenant.status,
      settings: req.tenant.settings,
      created_at: req.tenant.created_at
    };

    res.json(tenantInfo);
  } catch (error) {
    console.error('Error fetching current tenant:', error);
    res.status(500).json({ error: 'Failed to fetch tenant information' });
  }
});

// Update current tenant (requires admin role)
router.put('/current', 
  tenantMiddleware.requireTenantUser,
  tenantMiddleware.requireRole(['owner', 'admin']),
  async (req, res) => {
    try {
      const { name, settings } = req.body;
      const updates: any = {};

      if (name) updates.name = name;
      if (settings) updates.settings = settings;

      if (Object.keys(updates).length === 0) {
        return res.status(400).json({ error: 'No valid fields to update' });
      }

      const updatedTenant = await tenantService.updateTenant(req.tenant!.id, updates);
      
      res.json({
        message: 'Tenant updated successfully',
        tenant: {
          id: updatedTenant.id,
          name: updatedTenant.name,
          subdomain: updatedTenant.subdomain,
          plan_type: updatedTenant.plan_type,
          status: updatedTenant.status,
          settings: updatedTenant.settings,
          updated_at: updatedTenant.updated_at
        }
      });
    } catch (error) {
      console.error('Error updating tenant:', error);
      res.status(400).json({ 
        error: error instanceof Error ? error.message : 'Failed to update tenant' 
      });
    }
  }
);

// Get tenant users (requires admin role)
router.get('/users',
  tenantMiddleware.requireTenantUser,
  tenantMiddleware.requireRole(['owner', 'admin']),
  (req, res) => {
    try {
      const users = tenantService.getTenantUsers(req.tenant!.id);
      
      // Remove sensitive data
      const safeUsers = users.map(user => ({
        id: user.id,
        email: user.email,
        role: user.role,
        status: user.status,
        profile: user.profile,
        created_at: user.created_at,
        last_login: user.last_login
      }));

      res.json(safeUsers);
    } catch (error) {
      console.error('Error fetching tenant users:', error);
      res.status(500).json({ error: 'Failed to fetch tenant users' });
    }
  }
);

// Get current user information
router.get('/me', tenantMiddleware.requireTenantUser, (req, res) => {
  try {
    const user = req.tenantUser!;
    
    res.json({
      id: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
      profile: user.profile,
      permissions: user.permissions,
      created_at: user.created_at,
      last_login: user.last_login,
      tenant: {
        id: req.tenant!.id,
        name: req.tenant!.name,
        subdomain: req.tenant!.subdomain,
        plan_type: req.tenant!.plan_type
      }
    });
  } catch (error) {
    console.error('Error fetching current user:', error);
    res.status(500).json({ error: 'Failed to fetch user information' });
  }
});

// Admin routes (system-level, not tenant-specific)
router.get('/admin/all',
  // Add system admin authentication here
  (req, res) => {
    try {
      const tenants = tenantService.getAllTenants();
      res.json(tenants);
    } catch (error) {
      console.error('Error fetching all tenants:', error);
      res.status(500).json({ error: 'Failed to fetch tenants' });
    }
  }
);

// Reset tenant user password (admin only)
router.post('/admin/reset-password',
  // Add system admin authentication here
  async (req, res) => {
    try {
      const { tenantId, email, newPassword } = req.body;

      if (!tenantId || !email || !newPassword) {
        return res.status(400).json({
          error: 'Missing required fields: tenantId, email, newPassword'
        });
      }

      // Validate password length
      if (newPassword.length < 6) {
        return res.status(400).json({
          error: 'Password must be at least 6 characters long'
        });
      }

      // Get tenant
      const tenant = tenantService.getTenantById(tenantId);
      if (!tenant) {
        return res.status(404).json({ error: 'Tenant not found' });
      }

      // Get user
      const user = tenantService.getTenantUser(tenantId, email);
      if (!user) {
        return res.status(404).json({ error: 'User not found in tenant' });
      }

      // Hash new password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password in database
      const masterDb = tenantService.getMasterDatabase();
      masterDb.prepare(`
        UPDATE tenant_users
        SET password_hash = ?, updated_at = ?
        WHERE id = ?
      `).run(hashedPassword, new Date().toISOString(), user.id);

      console.log(`✅ Password reset for user: ${email} in tenant: ${tenant.name}`);

      res.json({
        message: 'Password reset successfully',
        user: {
          id: user.id,
          email: user.email,
          tenant: {
            id: tenant.id,
            name: tenant.name,
            subdomain: tenant.subdomain
          }
        }
      });
    } catch (error) {
      console.error('Error resetting password:', error);
      res.status(500).json({ error: 'Failed to reset password' });
    }
  }
);

router.get('/admin/stats',
  // Add system admin authentication here
  (req, res) => {
    try {
      const stats = tenantService.getTenantStats();
      res.json(stats);
    } catch (error) {
      console.error('Error fetching tenant stats:', error);
      res.status(500).json({ error: 'Failed to fetch tenant statistics' });
    }
  }
);

// Health check for tenant system
router.get('/health', (req, res) => {
  try {
    const stats = tenantService.getTenantStats();
    res.json({
      status: 'ok',
      multiTenantEnabled: process.env.ENABLE_MULTI_TENANCY === 'true',
      tenantStats: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Tenant health check failed:', error);
    res.status(500).json({
      status: 'error',
      error: 'Tenant system health check failed'
    });
  }
});

export default router;
