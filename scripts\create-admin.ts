import { initializeApp } from 'firebase/app';
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth';
import { getFirestore, doc, setDoc } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: "AIzaSyAUDG_4bTWzGXAEqwohxBrqCX0jaOy5YJs",
  authDomain: "shoe-repair-pos.firebaseapp.com",
  projectId: "shoe-repair-pos",
  storageBucket: "shoe-repair-pos.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:caca74f072d776a988c255"
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);

async function createAdminUser() {
  try {
    // Create user in Authentication
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      '<EMAIL>',
      'Admin123!'
    );

    const user = userCredential.user;
    console.log('User created successfully:', user.uid);

    // Create user document in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      uid: user.uid,
      email: '<EMAIL>',
      role: 'admin',
      name: 'Admin User',
      createdAt: new Date().toISOString()
    });

    console.log('Admin user created successfully in both Auth and Firestore!');
    console.log('You can now log in with:');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin123!');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

createAdminUser();
