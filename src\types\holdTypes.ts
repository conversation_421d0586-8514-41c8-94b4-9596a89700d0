export type HoldStatus = 'pending' | 'in-progress' | 'ready' | 'completed';

export interface HoldItem {
  id: string;
  customer_name: string;
  customer_phone: string | null;
  customer_email: string | null;
  item_description: string;
  hold_date: string;
  expected_completion: string | null;
  notes: string | null;
  status: HoldStatus;
  is_quick_drop: number; // SQLite stores booleans as 0/1
  created_at: string;
  updated_at: string;
}

export interface HoldStats {
  totalHoldItems: number;
  totalQuickDrops: number;
  dueToday: number;
  pendingItems: number;
  inProgressItems: number;
  readyItems: number;
}

export interface HoldFormData {
  customer_name: string;
  customer_phone: string;
  customer_email: string;
  item_description: string;
  expected_completion: string;
  notes: string;
  status: HoldStatus;
  is_quick_drop: boolean;
}
