import { db } from './db';
import { v4 as uuidv4 } from 'uuid';

export interface Category {
    id: string;
    name: string;
    description?: string;
    imageUrl?: string;
    created_at?: string;
    updated_at?: string;
}

export interface Product {
    id: string;
    name: string;
    description?: string;
    price: number;
    categoryId: string;
    imageUrl?: string;
    inStock: boolean;
    featured: boolean;
    quantity: number;
    created_at?: string;
    updated_at?: string;
}

export const categoryDb = {
    // Category operations
    getAllCategories: (): Category[] => {
        const stmt = db.prepare('SELECT * FROM categories ORDER BY name');
        return stmt.all() as Category[];
    },

    getCategoryById: (id: string): Category | undefined => {
        const stmt = db.prepare('SELECT * FROM categories WHERE id = ?');
        return stmt.get(id) as Category | undefined;
    },

    createCategory: (category: Omit<Category, 'id' | 'created_at' | 'updated_at'>) => {
        const id = uuidv4();
        const stmt = db.prepare(
            'INSERT INTO categories (id, name, description, image_url) VALUES (?, ?, ?, ?)'
        );
        stmt.run(id, category.name, category.description, category.imageUrl);
        return id;
    },

    updateCategory: (id: string, category: Partial<Category>) => {
        const updates = [];
        const values = [];
        
        if (category.name !== undefined) {
            updates.push('name = ?');
            values.push(category.name);
        }
        if (category.description !== undefined) {
            updates.push('description = ?');
            values.push(category.description);
        }
        if (category.imageUrl !== undefined) {
            updates.push('image_url = ?');
            values.push(category.imageUrl);
        }
        
        if (updates.length === 0) return;
        
        values.push(id);
        const stmt = db.prepare(
            `UPDATE categories SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
        );
        return stmt.run(...values);
    },

    deleteCategory: (id: string) => {
        const stmt = db.prepare('DELETE FROM categories WHERE id = ?');
        return stmt.run(id);
    },

    // Product operations
    getAllProducts: (): Product[] => {
        const stmt = db.prepare(`
            SELECT 
                id,
                name,
                description,
                price,
                category_id as categoryId,
                image_url as imageUrl,
                in_stock as inStock,
                featured,
                quantity,
                created_at,
                updated_at
            FROM products 
            ORDER BY name
        `);
        return stmt.all() as Product[];
    },

    getProductsByCategory: (categoryId: string): Product[] => {
        const stmt = db.prepare(`
            SELECT 
                id,
                name,
                description,
                price,
                category_id as categoryId,
                image_url as imageUrl,
                in_stock as inStock,
                featured,
                quantity,
                created_at,
                updated_at
            FROM products 
            WHERE category_id = ?
            ORDER BY name
        `);
        return stmt.all(categoryId) as Product[];
    },

    createProduct: (product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
        const id = uuidv4();
        const stmt = db.prepare(`
            INSERT INTO products (
                id, 
                name, 
                description, 
                price, 
                category_id,
                image_url,
                in_stock,
                featured,
                quantity
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        stmt.run(
            id,
            product.name,
            product.description,
            product.price,
            product.categoryId,
            product.imageUrl,
            product.inStock ? 1 : 0,
            product.featured ? 1 : 0,
            product.quantity
        );
        return id;
    },

    updateProduct: (id: string, product: Partial<Product>) => {
        const updates = [];
        const values = [];
        
        if (product.name !== undefined) {
            updates.push('name = ?');
            values.push(product.name);
        }
        if (product.description !== undefined) {
            updates.push('description = ?');
            values.push(product.description);
        }
        if (product.price !== undefined) {
            updates.push('price = ?');
            values.push(product.price);
        }
        if (product.categoryId !== undefined) {
            updates.push('category_id = ?');
            values.push(product.categoryId);
        }
        if (product.imageUrl !== undefined) {
            updates.push('image_url = ?');
            values.push(product.imageUrl);
        }
        if (product.inStock !== undefined) {
            updates.push('in_stock = ?');
            values.push(product.inStock ? 1 : 0);
        }
        if (product.featured !== undefined) {
            updates.push('featured = ?');
            values.push(product.featured ? 1 : 0);
        }
        if (product.quantity !== undefined) {
            updates.push('quantity = ?');
            values.push(product.quantity);
        }
        
        if (updates.length === 0) return;
        
        values.push(id);
        const stmt = db.prepare(
            `UPDATE products SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`
        );
        return stmt.run(...values);
    },

    deleteProduct: (id: string) => {
        const stmt = db.prepare('DELETE FROM products WHERE id = ?');
        return stmt.run(id);
    }
};
