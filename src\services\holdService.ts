import axios from 'axios';
import { HoldItem, HoldStats, HoldFormData } from '../types/holdTypes';

const API_URL = '/api/holds';

export const getHoldItems = async (): Promise<HoldItem[]> => {
  try {
    const response = await axios.get<HoldItem[]>(API_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching hold items:', error);
    throw error;
  }
};

export const getHoldItemById = async (id: string): Promise<HoldItem> => {
  try {
    const response = await axios.get<HoldItem>(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching hold item with ID ${id}:`, error);
    throw error;
  }
};

export const createHoldItem = async (holdItem: HoldFormData): Promise<HoldItem> => {
  try {
    const response = await axios.post<HoldItem>(API_URL, holdItem);
    return response.data;
  } catch (error) {
    console.error('Error creating hold item:', error);
    throw error;
  }
};

export const updateHoldItem = async (id: string, holdItem: Partial<HoldFormData>): Promise<HoldItem> => {
  try {
    const response = await axios.put<HoldItem>(`${API_URL}/${id}`, holdItem);
    return response.data;
  } catch (error) {
    console.error(`Error updating hold item with ID ${id}:`, error);
    throw error;
  }
};

export const deleteHoldItem = async (id: string): Promise<void> => {
  try {
    await axios.delete(`${API_URL}/${id}`);
  } catch (error) {
    console.error(`Error deleting hold item with ID ${id}:`, error);
    throw error;
  }
};

export const getHoldStats = async (): Promise<HoldStats> => {
  try {
    const response = await axios.get<HoldStats>(`${API_URL}/stats/summary`);
    return response.data;
  } catch (error) {
    console.error('Error fetching hold stats:', error);
    throw error;
  }
};

export const getTodayTimeline = async (): Promise<HoldItem[]> => {
  try {
    const response = await axios.get<HoldItem[]>(`${API_URL}/timeline/today`);
    return response.data;
  } catch (error) {
    console.error('Error fetching today\'s timeline:', error);
    throw error;
  }
};
