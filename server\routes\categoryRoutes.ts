import express from 'express';
import { categoryDb } from '../database/categoryDb';

const router = express.Router();

// Category routes
router.get('/categories', (req, res) => {
    try {
        const categories = categoryDb.getAllCategories();
        res.json(categories);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch categories' });
    }
});

router.post('/categories', (req, res) => {
    try {
        const category = req.body;
        const id = categoryDb.createCategory(category);
        res.status(201).json({ id });
    } catch (error) {
        res.status(500).json({ error: 'Failed to create category' });
    }
});

router.put('/categories/:id', (req, res) => {
    try {
        const { id } = req.params;
        const category = req.body;
        categoryDb.updateCategory(id, category);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: 'Failed to update category' });
    }
});

router.delete('/categories/:id', (req, res) => {
    try {
        const { id } = req.params;
        categoryDb.deleteCategory(id);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete category' });
    }
});

// Product routes
router.get('/products', (req, res) => {
    try {
        const products = categoryDb.getAllProducts();
        res.json(products);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch products' });
    }
});

router.get('/categories/:categoryId/products', (req, res) => {
    try {
        const { categoryId } = req.params;
        const products = categoryDb.getProductsByCategory(categoryId);
        res.json(products);
    } catch (error) {
        res.status(500).json({ error: 'Failed to fetch products' });
    }
});

router.post('/products', (req, res) => {
    try {
        const product = req.body;
        const id = categoryDb.createProduct(product);
        res.status(201).json({ id });
    } catch (error) {
        res.status(500).json({ error: 'Failed to create product' });
    }
});

router.put('/products/:id', (req, res) => {
    try {
        const { id } = req.params;
        const product = req.body;
        categoryDb.updateProduct(id, product);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: 'Failed to update product' });
    }
});

router.delete('/products/:id', (req, res) => {
    try {
        const { id } = req.params;
        categoryDb.deleteProduct(id);
        res.json({ success: true });
    } catch (error) {
        res.status(500).json({ error: 'Failed to delete product' });
    }
});

export default router;
