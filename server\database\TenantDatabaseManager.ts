import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { Tenant } from '../types/tenant';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class TenantDatabaseManager {
  private static instance: TenantDatabaseManager;
  private masterDb: Database.Database;
  private tenantDatabases: Map<string, Database.Database> = new Map();
  private tenantsDir: string;

  private constructor() {
    // Initialize master database for tenant management
    const masterDbPath = path.join(__dirname, '..', 'master.db');
    this.masterDb = new Database(masterDbPath);
    
    // Set up tenants directory
    this.tenantsDir = path.join(__dirname, '..', 'tenants');
    this.ensureTenantsDirectory();
    
    // Initialize master database schema
    this.initializeMasterDatabase();
    
    console.log('✅ TenantDatabaseManager initialized');
  }

  public static getInstance(): TenantDatabaseManager {
    if (!TenantDatabaseManager.instance) {
      TenantDatabaseManager.instance = new TenantDatabaseManager();
    }
    return TenantDatabaseManager.instance;
  }

  private ensureTenantsDirectory(): void {
    if (!fs.existsSync(this.tenantsDir)) {
      fs.mkdirSync(this.tenantsDir, { recursive: true });
      console.log(`✅ Created tenants directory: ${this.tenantsDir}`);
    }
  }

  private initializeMasterDatabase(): void {
    try {
      // Enable foreign keys and set pragmas
      this.masterDb.pragma('foreign_keys = ON');
      this.masterDb.pragma('journal_mode = WAL');
      this.masterDb.pragma('synchronous = NORMAL');
      
      // Load tenant schema
      const tenantSchemaPath = path.join(__dirname, 'tenant_schema.sql');
      const tenantSchema = fs.readFileSync(tenantSchemaPath, 'utf8');
      this.masterDb.exec(tenantSchema);
      
      console.log('✅ Master database schema loaded');
    } catch (error) {
      console.error('❌ Failed to initialize master database:', error);
      throw error;
    }
  }

  public getMasterDatabase(): Database.Database {
    return this.masterDb;
  }

  public async getTenantDatabase(tenantId: string): Promise<Database.Database> {
    // Check if database is already cached
    if (this.tenantDatabases.has(tenantId)) {
      return this.tenantDatabases.get(tenantId)!;
    }

    // Get tenant info from master database
    const tenant = this.getTenantById(tenantId);
    if (!tenant) {
      throw new Error(`Tenant not found: ${tenantId}`);
    }

    // Get or create tenant database
    const dbPath = this.getTenantDatabasePath(tenantId);
    const tenantDb = new Database(dbPath);
    
    // Configure database
    tenantDb.pragma('foreign_keys = ON');
    tenantDb.pragma('journal_mode = WAL');
    tenantDb.pragma('synchronous = NORMAL');
    
    // Initialize schema if new database
    if (!fs.existsSync(dbPath) || this.isDatabaseEmpty(tenantDb)) {
      await this.initializeTenantDatabase(tenantDb, tenant);
    }
    
    // Cache the database connection
    this.tenantDatabases.set(tenantId, tenantDb);
    
    // Update database registry
    this.updateTenantDatabaseRegistry(tenantId, dbPath);
    
    console.log(`✅ Tenant database ready: ${tenantId}`);
    return tenantDb;
  }

  private getTenantDatabasePath(tenantId: string): string {
    return path.join(this.tenantsDir, `${tenantId}.db`);
  }

  private isDatabaseEmpty(db: Database.Database): boolean {
    try {
      const tables = db.prepare(`
        SELECT COUNT(*) as count 
        FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).get() as { count: number };
      return tables.count === 0;
    } catch {
      return true;
    }
  }

  private async initializeTenantDatabase(db: Database.Database, tenant: Tenant): Promise<void> {
    try {
      // Load the main business schema (from unified_schema.sql)
      const businessSchemaPath = path.join(__dirname, 'unified_schema.sql');
      const businessSchema = fs.readFileSync(businessSchemaPath, 'utf8');
      db.exec(businessSchema);
      
      // Add tenant-specific metadata
      db.exec(`
        CREATE TABLE IF NOT EXISTS tenant_metadata (
          key TEXT PRIMARY KEY,
          value TEXT,
          updated_at TEXT DEFAULT CURRENT_TIMESTAMP
        );
        
        INSERT OR REPLACE INTO tenant_metadata (key, value) VALUES 
          ('tenant_id', '${tenant.id}'),
          ('tenant_name', '${tenant.name}'),
          ('created_at', '${new Date().toISOString()}'),
          ('schema_version', '1.0.0');
      `);
      
      console.log(`✅ Initialized database for tenant: ${tenant.name}`);
    } catch (error) {
      console.error(`❌ Failed to initialize tenant database for ${tenant.id}:`, error);
      throw error;
    }
  }

  public getTenantById(tenantId: string): Tenant | null {
    try {
      const row = this.masterDb.prepare(`
        SELECT * FROM tenants WHERE id = ?
      `).get(tenantId);
      
      if (!row) return null;
      
      return {
        ...row,
        settings: JSON.parse(row.settings || '{}')
      } as Tenant;
    } catch (error) {
      console.error(`Error fetching tenant ${tenantId}:`, error);
      return null;
    }
  }

  public getTenantBySubdomain(subdomain: string): Tenant | null {
    try {
      const row = this.masterDb.prepare(`
        SELECT * FROM tenants WHERE subdomain = ? AND status = 'active'
      `).get(subdomain);
      
      if (!row) return null;
      
      return {
        ...row,
        settings: JSON.parse(row.settings || '{}')
      } as Tenant;
    } catch (error) {
      console.error(`Error fetching tenant by subdomain ${subdomain}:`, error);
      return null;
    }
  }

  public createTenant(tenantData: Partial<Tenant>): Tenant {
    try {
      const now = new Date().toISOString();
      const tenant: Tenant = {
        id: tenantData.id || this.generateTenantId(),
        name: tenantData.name || 'New Tenant',
        subdomain: tenantData.subdomain || '',
        plan_type: tenantData.plan_type || 'basic',
        status: tenantData.status || 'active',
        settings: tenantData.settings || {},
        created_at: now,
        updated_at: now,
        ...tenantData
      };

      // Insert into master database
      this.masterDb.prepare(`
        INSERT INTO tenants (
          id, name, subdomain, plan_type, status, settings, 
          owner_email, billing_email, max_users, max_operations, 
          storage_limit_mb, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        tenant.id,
        tenant.name,
        tenant.subdomain,
        tenant.plan_type,
        tenant.status,
        JSON.stringify(tenant.settings),
        tenant.owner_email || null,
        tenant.billing_email || null,
        tenant.max_users || null,
        tenant.max_operations || null,
        tenant.storage_limit_mb || null,
        tenant.created_at,
        tenant.updated_at
      );

      console.log(`✅ Created tenant: ${tenant.name} (${tenant.id})`);
      return tenant;
    } catch (error) {
      console.error('❌ Failed to create tenant:', error);
      throw error;
    }
  }

  private generateTenantId(): string {
    return `tenant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateTenantDatabaseRegistry(tenantId: string, dbPath: string): void {
    try {
      const stats = fs.statSync(dbPath);
      const sizeInMB = stats.size / (1024 * 1024);
      
      this.masterDb.prepare(`
        INSERT OR REPLACE INTO tenant_databases (
          id, tenant_id, database_path, database_size_mb, updated_at
        ) VALUES (?, ?, ?, ?, ?)
      `).run(
        `${tenantId}_db`,
        tenantId,
        dbPath,
        sizeInMB,
        new Date().toISOString()
      );
    } catch (error) {
      console.error('Failed to update tenant database registry:', error);
    }
  }

  public getAllTenants(): Tenant[] {
    try {
      const rows = this.masterDb.prepare(`
        SELECT * FROM tenants ORDER BY created_at DESC
      `).all();
      
      return rows.map(row => ({
        ...row,
        settings: JSON.parse(row.settings || '{}')
      })) as Tenant[];
    } catch (error) {
      console.error('Error fetching all tenants:', error);
      return [];
    }
  }

  public getTenantStats(): any {
    try {
      const stats = this.masterDb.prepare(`
        SELECT 
          COUNT(*) as total_tenants,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active_tenants,
          COUNT(CASE WHEN plan_type = 'trial' THEN 1 END) as trial_tenants,
          COUNT(CASE WHEN plan_type = 'basic' THEN 1 END) as basic_tenants,
          COUNT(CASE WHEN plan_type = 'professional' THEN 1 END) as professional_tenants,
          COUNT(CASE WHEN plan_type = 'enterprise' THEN 1 END) as enterprise_tenants
        FROM tenants
      `).get();
      
      return stats;
    } catch (error) {
      console.error('Error fetching tenant stats:', error);
      return null;
    }
  }

  public closeTenantDatabase(tenantId: string): void {
    const db = this.tenantDatabases.get(tenantId);
    if (db) {
      db.close();
      this.tenantDatabases.delete(tenantId);
      console.log(`✅ Closed database for tenant: ${tenantId}`);
    }
  }

  public closeAllConnections(): void {
    // Close all tenant databases
    for (const [tenantId, db] of this.tenantDatabases) {
      db.close();
      console.log(`✅ Closed database for tenant: ${tenantId}`);
    }
    this.tenantDatabases.clear();
    
    // Close master database
    if (this.masterDb) {
      this.masterDb.close();
      console.log('✅ Closed master database');
    }
  }
}
