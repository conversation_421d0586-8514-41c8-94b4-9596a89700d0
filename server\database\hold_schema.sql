-- Hold items and quick drops tables
CREATE TABLE IF NOT EXISTS hold_items (
    id TEXT PRIMARY KEY,
    customer_name TEXT NOT NULL,
    customer_phone TEXT,
    customer_email TEXT,
    item_description TEXT NOT NULL,
    hold_date TEXT DEFAULT CURRENT_TIMESTAMP,
    expected_completion TEXT,
    notes TEXT,
    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'in-progress', 'ready', 'completed')),
    is_quick_drop BOOLEAN DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO hold_items (id, customer_name, customer_phone, customer_email, item_description, hold_date, expected_completion, notes, status, is_quick_drop) VALUES
('hold1', '<PERSON>', '************', '<EMAIL>', 'Leather Shoes - Polish & Repair', '2025-02-25 10:00:00', '2025-03-01 14:00:00', 'Customer requested extra shine', 'in-progress', 0),
('hold2', '<PERSON>', '************', '<EMAIL>', '<PERSON>ot Repair - Heel Replacement', '2025-02-27 14:30:00', '2025-03-01 15:30:00', 'Left boot has worn heel', 'pending', 0),
('hold3', 'James Wilson', '************', '<EMAIL>', '2x Dress Shoes - Polish & Repair', '2025-01-18 09:15:00', '2025-03-02 12:00:00', 'Black and brown dress shoes', 'pending', 0),
('hold4', 'Emily Johnson', '************', '<EMAIL>', 'Suede Boots - Cleaning & Waterproofing', '2025-02-28 11:45:00', '2025-03-01 16:00:00', 'Be careful with suede', 'ready', 0),
('hold5', 'Michael Brown', '************', '<EMAIL>', 'Running Shoes - Sole Replacement', '2025-02-26 13:20:00', '2025-03-03 10:00:00', 'Customer is a marathon runner', 'in-progress', 0),
('qd1', 'Lisa Taylor', '555-345-6789', '<EMAIL>', 'Quick Polish - Business Shoes', '2025-03-01 09:30:00', '2025-03-01 11:30:00', 'Customer waiting in store', 'pending', 1),
('qd2', 'Robert Garcia', '************', '<EMAIL>', 'Quick Clean - White Sneakers', '2025-03-01 10:15:00', '2025-03-01 12:15:00', 'Remove scuff marks', 'in-progress', 1),
('qd3', 'Jennifer Martinez', '555-567-8901', '<EMAIL>', 'Quick Repair - Heel Tip', '2025-03-01 10:45:00', '2025-03-01 12:45:00', 'Right shoe only', 'ready', 1);
