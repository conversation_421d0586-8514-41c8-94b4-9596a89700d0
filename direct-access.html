<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Direct Dashboard Access</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #1a202c;
      color: white;
    }
    .container {
      text-align: center;
      padding: 2rem;
      border-radius: 0.5rem;
      background-color: #2d3748;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    h1 {
      margin-bottom: 1.5rem;
    }
    button {
      background-color: #4299e1;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.25rem;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #3182ce;
    }
    .info {
      margin-top: 1.5rem;
      font-size: 0.875rem;
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Shoe Repair POS Dashboard Access</h1>
    <p>Click the button below to bypass login and access the dashboard directly.</p>
    <button id="accessBtn">Access Dashboard</button>
    <p class="info">This will automatically log you in as the admin user.</p>
  </div>

  <script>
    document.getElementById('accessBtn').addEventListener('click', function() {
      // Set mock user data in localStorage
      const adminUser = {
        id: '1',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        permissions: ['all'],
        active: true,
        lastLogin: '2024-03-15T08:30:00Z',
        isAuthenticated: true
      };
      
      // Store user in localStorage
      localStorage.setItem('user', JSON.stringify(adminUser));
      
      // Redirect to dashboard
      window.location.href = 'http://localhost:5173/';
    });
  </script>
</body>
</html>
