import { Request, Response, NextFunction } from 'express';
import { TenantService } from '../services/TenantService';
import { Tenant, TenantUser } from '../types/tenant';
import Database from 'better-sqlite3';

// Extend Express Request interface to include tenant context
declare global {
  namespace Express {
    interface Request {
      tenant?: Tenant;
      tenantUser?: TenantUser;
      tenantDb?: Database.Database;
      isMultiTenant?: boolean;
    }
  }
}

export class TenantMiddleware {
  private tenantService: TenantService;

  constructor() {
    this.tenantService = new TenantService();
  }

  // Middleware to resolve tenant from subdomain
  resolveTenant = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if multi-tenancy is enabled
      const isMultiTenantEnabled = process.env.ENABLE_MULTI_TENANCY === 'true';
      req.isMultiTenant = isMultiTenantEnabled;

      if (!isMultiTenantEnabled) {
        // Use default tenant for backward compatibility
        const defaultTenant = this.tenantService.getTenantById('default');
        if (defaultTenant) {
          req.tenant = defaultTenant;
          req.tenantDb = await this.tenantService.getTenantDatabase('default');
        }
        return next();
      }

      // Extract subdomain from hostname
      const hostname = req.get('host') || '';
      const subdomain = this.extractSubdomain(hostname);

      if (!subdomain) {
        return res.status(400).json({ 
          error: 'Invalid hostname - subdomain required',
          code: 'SUBDOMAIN_REQUIRED'
        });
      }

      // Resolve tenant by subdomain
      const tenant = this.tenantService.getTenantBySubdomain(subdomain);
      
      if (!tenant) {
        return res.status(404).json({ 
          error: 'Tenant not found',
          code: 'TENANT_NOT_FOUND',
          subdomain: subdomain
        });
      }

      if (tenant.status !== 'active') {
        return res.status(403).json({ 
          error: 'Tenant is not active',
          code: 'TENANT_INACTIVE',
          status: tenant.status
        });
      }

      // Set tenant context
      req.tenant = tenant;
      req.tenantDb = await this.tenantService.getTenantDatabase(tenant.id);

      console.log(`🏢 Tenant resolved: ${tenant.name} (${tenant.subdomain})`);
      next();
    } catch (error) {
      console.error('❌ Tenant resolution failed:', error);
      res.status(500).json({ 
        error: 'Tenant resolution failed',
        code: 'TENANT_RESOLUTION_ERROR'
      });
    }
  };

  // Middleware to resolve tenant user from authentication
  resolveTenantUser = async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Skip if no tenant context
      if (!req.tenant) {
        return next();
      }

      // Extract user information from various auth sources
      let userEmail: string | null = null;
      let firebaseUid: string | null = null;

      // Check for Firebase user (from Firebase Auth middleware)
      if (req.user && (req.user as any).email) {
        userEmail = (req.user as any).email;
        firebaseUid = (req.user as any).uid;
      }

      // Check for user in session/token
      if (!userEmail && req.headers.authorization) {
        // Handle JWT or other token-based auth
        userEmail = this.extractUserFromToken(req.headers.authorization);
      }

      // Check for user in session
      if (!userEmail && req.session && (req.session as any).user) {
        userEmail = (req.session as any).user.email;
      }

      if (!userEmail) {
        // No user authentication found - continue without tenant user
        return next();
      }

      // Resolve tenant user
      let tenantUser: TenantUser | null = null;

      if (firebaseUid) {
        tenantUser = this.tenantService.getTenantUserByFirebaseUid(firebaseUid);
      } else {
        tenantUser = this.tenantService.getTenantUser(req.tenant.id, userEmail);
      }

      if (tenantUser && tenantUser.status === 'active') {
        req.tenantUser = tenantUser;
        console.log(`👤 Tenant user resolved: ${tenantUser.email} (${tenantUser.role})`);
      }

      next();
    } catch (error) {
      console.error('❌ Tenant user resolution failed:', error);
      // Don't fail the request, just continue without tenant user
      next();
    }
  };

  // Middleware to require tenant context
  requireTenant = (req: Request, res: Response, next: NextFunction) => {
    if (!req.tenant) {
      return res.status(400).json({ 
        error: 'Tenant context required',
        code: 'TENANT_REQUIRED'
      });
    }
    next();
  };

  // Middleware to require tenant user authentication
  requireTenantUser = (req: Request, res: Response, next: NextFunction) => {
    if (!req.tenantUser) {
      return res.status(401).json({ 
        error: 'Tenant user authentication required',
        code: 'TENANT_AUTH_REQUIRED'
      });
    }
    next();
  };

  // Middleware to require specific role
  requireRole = (roles: string | string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.tenantUser) {
        return res.status(401).json({ 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const allowedRoles = Array.isArray(roles) ? roles : [roles];
      
      if (!allowedRoles.includes(req.tenantUser.role)) {
        return res.status(403).json({ 
          error: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: allowedRoles,
          current: req.tenantUser.role
        });
      }

      next();
    };
  };

  // Middleware to check tenant plan features
  requireFeature = (feature: string) => {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.tenant) {
        return res.status(400).json({ 
          error: 'Tenant context required',
          code: 'TENANT_REQUIRED'
        });
      }

      const features = req.tenant.settings?.features || {};
      
      if (!features[feature as keyof typeof features]) {
        return res.status(403).json({ 
          error: 'Feature not available in current plan',
          code: 'FEATURE_NOT_AVAILABLE',
          feature: feature,
          plan: req.tenant.plan_type
        });
      }

      next();
    };
  };

  // Utility methods
  private extractSubdomain(hostname: string): string | null {
    // Remove port if present
    const host = hostname.split(':')[0];
    
    // Split by dots
    const parts = host.split('.');
    
    // For localhost development, check for specific patterns
    if (host.includes('localhost') || host.includes('127.0.0.1')) {
      // Look for subdomain in format: subdomain.localhost:port
      if (parts.length >= 2 && parts[0] !== 'localhost') {
        return parts[0];
      }
      return null;
    }

    // For production domains, extract subdomain
    if (parts.length >= 3) {
      return parts[0];
    }

    return null;
  }

  private extractUserFromToken(authorization: string): string | null {
    try {
      // This is a placeholder - implement based on your JWT/token strategy
      // For now, return null to skip token-based user extraction
      return null;
    } catch (error) {
      console.error('Failed to extract user from token:', error);
      return null;
    }
  }

  // Middleware to add tenant context to response headers (for debugging)
  addTenantHeaders = (req: Request, res: Response, next: NextFunction) => {
    if (req.tenant) {
      res.setHeader('X-Tenant-ID', req.tenant.id);
      res.setHeader('X-Tenant-Name', req.tenant.name);
      res.setHeader('X-Tenant-Plan', req.tenant.plan_type);
    }
    
    if (req.tenantUser) {
      res.setHeader('X-Tenant-User-Role', req.tenantUser.role);
    }

    res.setHeader('X-Multi-Tenant-Enabled', req.isMultiTenant ? 'true' : 'false');
    
    next();
  };

  // Middleware for tenant usage tracking
  trackUsage = (req: Request, res: Response, next: NextFunction) => {
    // Track API usage for billing/analytics
    if (req.tenant && req.method !== 'GET') {
      // Implement usage tracking logic here
      // This could increment counters for operations, API calls, etc.
    }
    next();
  };
}

// Export singleton instance
export const tenantMiddleware = new TenantMiddleware();
