import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Routes, Route, useNavigate, Navigate, Outlet } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import StorePage from './pages/StorePage';
import CustomerPage from './pages/CustomerPage';
import DropPage from './pages/DropPage';
import PickupPage from './pages/PickupPage';
import MessagePage from './pages/MessagePage';
import OperationPage from './pages/OperationPage';
import SuppliesPage from './pages/SuppliesPage';
import SalesPage from './pages/SalesPage';
import SalesItems from './pages/SalesItems';
import TicketsPage from './pages/TicketsPage';
import QRCodesPage from './pages/QRCodesPage';
import MarketingPage from './pages/MarketingPage';
import ReportsPage from './pages/ReportsPage';
import StaffPage from './pages/StaffPage';
import NotificationsPage from './pages/NotificationsPage';
import AdminPage from './pages/AdminPage';
import MainMenu from './components/MainMenu';
import RightSidebar from './components/RightSidebar';
import QuickActionButtons from './components/QuickActionButtons';
import { CustomerProvider } from './contexts/CustomerContext';
import { OperationProvider } from './contexts/OperationContext';
import { AdminProvider } from './contexts/AdminContext';
import { AuthProvider } from './contexts/AuthContext';
import { TenantProvider } from './contexts/TenantContext';
import { CartProvider } from './contexts/CartContext';
import { ProductProvider } from './contexts/ProductContext';
import ProtectedRoute from './components/ProtectedRoute';
import TenantRouter from './components/TenantRouter';
import LoginPage from './pages/LoginPage';
import FirestoreTest from './pages/FirestoreTest';
import HoldQuickDropPage from './pages/HoldQuickDropPage';
import NoChargeDoOverPage from './pages/NoChargeDoOverPage';
import TicketSearchPage from './pages/TicketSearchPage';
import AssemblyPage from './pages/AssemblyPage';
import RackingPage from './pages/RackingPage';
import PickupOrderPage from './pages/PickupOrderPage';
import DeliveriesPage from './pages/DeliveriesPage';
import CodPaymentPage from './pages/CodPaymentPage';
import SaleItemsPage from './pages/SaleItemsPage';
import ProductCategoryManager from './pages/ProductCategoryManager';

const Layout = ({ isSidebarCollapsed, toggleSidebar }: { isSidebarCollapsed: boolean; toggleSidebar: () => void }) => {
  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      <div
        className={`bg-gray-900 text-white transition-all duration-300 ease-in-out ${
          isSidebarCollapsed ? 'w-16' : 'w-64'
        }`}
      >
        <MainMenu isCollapsed={isSidebarCollapsed} />
      </div>

      {/* Toggle button */}
      <button
        onClick={toggleSidebar}
        className="fixed left-0 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white p-2 rounded-r-md hover:bg-gray-700 focus:outline-none"
      >
        {isSidebarCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
      </button>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden pr-[80px]">
        <div className="flex-1 overflow-auto">
          <Outlet />
        </div>
        <QuickActionButtons />
      </div>
    </div>
  );
};

function App() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  return (
    <div className="min-h-screen bg-gray-800 text-white">
      <AuthProvider>
        <TenantProvider>
          <CustomerProvider>
            <OperationProvider>
              <AdminProvider>
                <CartProvider>
                  <ProductProvider>
                    <Routes>
                      {/* Legacy routes for backward compatibility */}
                      <Route path="/test-firestore" element={<FirestoreTest />} />

                      {/* Main tenant-aware routing */}
                      <Route
                        path="/*"
                        element={
                          <TenantRouter onNewOrder={() => {}} />
                        }
                      />
                    </Routes>
                  </ProductProvider>
                </CartProvider>
              </AdminProvider>
            </OperationProvider>
          </CustomerProvider>
        </TenantProvider>
      </AuthProvider>
      <Toaster position="top-right" toastOptions={{
        style: {
          background: '#333',
          color: '#fff',
        },
      }} />
    </div>
  );
}

function AppWithProviders() {
  return (
    <App />
  );
}

export default AppWithProviders;