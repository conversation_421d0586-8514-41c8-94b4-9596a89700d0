import React from 'react';
import { Link } from 'react-router-dom';

const TenantNotFound: React.FC = () => {
  const currentSubdomain = window.location.hostname.split('.')[0];

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h2 className="text-3xl font-extrabold text-gray-900">
            Tenant Not Found
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            The tenant "{currentSubdomain}" could not be found.
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                What can you do?
              </h3>
              
              <div className="space-y-4">
                <div className="border rounded-lg p-4 text-left">
                  <h4 className="font-medium text-gray-900 mb-2">Check the URL</h4>
                  <p className="text-sm text-gray-600">
                    Make sure you've entered the correct subdomain. It should be in the format: 
                    <code className="bg-gray-100 px-1 rounded">yourstore.domain.com</code>
                  </p>
                </div>

                <div className="border rounded-lg p-4 text-left">
                  <h4 className="font-medium text-gray-900 mb-2">Create a New Store</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    If you don't have a store yet, you can create one now.
                  </p>
                  <Link
                    to="/onboarding"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Create New Store
                  </Link>
                </div>

                <div className="border rounded-lg p-4 text-left">
                  <h4 className="font-medium text-gray-900 mb-2">Contact Support</h4>
                  <p className="text-sm text-gray-600 mb-3">
                    If you believe this is an error, please contact our support team.
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Contact Support
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 text-center">
        <p className="text-xs text-gray-500">
          Shoe Repair POS - Professional Shoe Repair Management
        </p>
      </div>
    </div>
  );
};

export default TenantNotFound;
