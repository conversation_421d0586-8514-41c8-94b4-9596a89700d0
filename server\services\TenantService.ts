import { TenantDatabaseManager } from '../database/TenantDatabaseManager';
import { 
  Tenant, 
  TenantUser, 
  TenantInvitation, 
  TenantPlan,
  TenantStatus,
  createDefaultTenantSettings,
  validateSubdomain,
  isSubdomainReserved
} from '../types/tenant';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcrypt';

export class TenantService {
  private dbManager: TenantDatabaseManager;

  constructor() {
    this.dbManager = TenantDatabaseManager.getInstance();
  }

  // Tenant Management
  async createTenant(data: {
    name: string;
    subdomain: string;
    plan_type?: TenantPlan;
    owner_email: string;
    owner_name?: string;
    password?: string;
  }): Promise<{ tenant: Tenant; user: TenantUser }> {
    // Validate subdomain
    if (!validateSubdomain(data.subdomain)) {
      throw new Error('Invalid subdomain format');
    }

    if (isSubdomainReserved(data.subdomain)) {
      throw new Error('Subdomain is reserved');
    }

    // Check if subdomain is already taken
    const existingTenant = this.dbManager.getTenantBySubdomain(data.subdomain);
    if (existingTenant) {
      throw new Error('Subdomain already exists');
    }

    try {
      const tenantId = uuidv4();
      const planType = data.plan_type || 'basic';
      
      // Create tenant
      const tenant = this.dbManager.createTenant({
        id: tenantId,
        name: data.name,
        subdomain: data.subdomain.toLowerCase(),
        plan_type: planType,
        status: 'active',
        settings: createDefaultTenantSettings(planType),
        owner_email: data.owner_email,
        billing_email: data.owner_email
      });

      // Create owner user
      const user = await this.createTenantUser({
        tenant_id: tenantId,
        email: data.owner_email,
        password: data.password,
        role: 'owner',
        profile: {
          first_name: data.owner_name?.split(' ')[0] || '',
          last_name: data.owner_name?.split(' ').slice(1).join(' ') || ''
        }
      });

      // Initialize tenant database
      await this.dbManager.getTenantDatabase(tenantId);

      console.log(`✅ Created tenant: ${tenant.name} with owner: ${data.owner_email}`);
      return { tenant, user };
    } catch (error) {
      console.error('❌ Failed to create tenant:', error);
      throw error;
    }
  }

  getTenantById(tenantId: string): Tenant | null {
    return this.dbManager.getTenantById(tenantId);
  }

  getTenantBySubdomain(subdomain: string): Tenant | null {
    return this.dbManager.getTenantBySubdomain(subdomain);
  }

  async updateTenant(tenantId: string, updates: Partial<Tenant>): Promise<Tenant> {
    const masterDb = this.dbManager.getMasterDatabase();
    
    try {
      const updateFields: string[] = [];
      const values: any[] = [];

      if (updates.name) {
        updateFields.push('name = ?');
        values.push(updates.name);
      }

      if (updates.status) {
        updateFields.push('status = ?');
        values.push(updates.status);
      }

      if (updates.settings) {
        updateFields.push('settings = ?');
        values.push(JSON.stringify(updates.settings));
      }

      if (updates.plan_type) {
        updateFields.push('plan_type = ?');
        values.push(updates.plan_type);
      }

      if (updateFields.length === 0) {
        throw new Error('No valid fields to update');
      }

      updateFields.push('updated_at = ?');
      values.push(new Date().toISOString());
      values.push(tenantId);

      const result = masterDb.prepare(`
        UPDATE tenants SET ${updateFields.join(', ')} WHERE id = ?
      `).run(...values);

      if (result.changes === 0) {
        throw new Error('Tenant not found');
      }

      const updatedTenant = this.getTenantById(tenantId);
      if (!updatedTenant) {
        throw new Error('Failed to retrieve updated tenant');
      }

      console.log(`✅ Updated tenant: ${tenantId}`);
      return updatedTenant;
    } catch (error) {
      console.error('❌ Failed to update tenant:', error);
      throw error;
    }
  }

  // Tenant User Management
  async createTenantUser(data: {
    tenant_id: string;
    email: string;
    password?: string;
    role: string;
    profile?: any;
    firebase_uid?: string;
  }): Promise<TenantUser> {
    const masterDb = this.dbManager.getMasterDatabase();
    
    try {
      const userId = uuidv4();
      const now = new Date().toISOString();
      
      // Hash password if provided
      let passwordHash = null;
      if (data.password) {
        passwordHash = await bcrypt.hash(data.password, 10);
      }

      const user: TenantUser = {
        id: userId,
        tenant_id: data.tenant_id,
        email: data.email,
        password_hash: passwordHash,
        role: data.role as any,
        permissions: [],
        status: 'active',
        profile: data.profile || {},
        firebase_uid: data.firebase_uid,
        created_at: now,
        updated_at: now
      };

      masterDb.prepare(`
        INSERT INTO tenant_users (
          id, tenant_id, email, password_hash, role, permissions, 
          status, profile, firebase_uid, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        user.id,
        user.tenant_id,
        user.email,
        user.password_hash,
        user.role,
        JSON.stringify(user.permissions),
        user.status,
        JSON.stringify(user.profile),
        user.firebase_uid,
        user.created_at,
        user.updated_at
      );

      console.log(`✅ Created tenant user: ${user.email} for tenant: ${data.tenant_id}`);
      return user;
    } catch (error) {
      console.error('❌ Failed to create tenant user:', error);
      throw error;
    }
  }

  getTenantUser(tenantId: string, email: string): TenantUser | null {
    const masterDb = this.dbManager.getMasterDatabase();
    
    try {
      const row = masterDb.prepare(`
        SELECT * FROM tenant_users 
        WHERE tenant_id = ? AND email = ? AND status = 'active'
      `).get(tenantId, email);

      if (!row) return null;

      return {
        ...row,
        permissions: JSON.parse(row.permissions || '[]'),
        profile: JSON.parse(row.profile || '{}')
      } as TenantUser;
    } catch (error) {
      console.error('Error fetching tenant user:', error);
      return null;
    }
  }

  getTenantUserByFirebaseUid(firebaseUid: string): TenantUser | null {
    const masterDb = this.dbManager.getMasterDatabase();
    
    try {
      const row = masterDb.prepare(`
        SELECT * FROM tenant_users 
        WHERE firebase_uid = ? AND status = 'active'
      `).get(firebaseUid);

      if (!row) return null;

      return {
        ...row,
        permissions: JSON.parse(row.permissions || '[]'),
        profile: JSON.parse(row.profile || '{}')
      } as TenantUser;
    } catch (error) {
      console.error('Error fetching tenant user by Firebase UID:', error);
      return null;
    }
  }

  getTenantUsers(tenantId: string): TenantUser[] {
    const masterDb = this.dbManager.getMasterDatabase();
    
    try {
      const rows = masterDb.prepare(`
        SELECT * FROM tenant_users 
        WHERE tenant_id = ? 
        ORDER BY created_at ASC
      `).all(tenantId);

      return rows.map(row => ({
        ...row,
        permissions: JSON.parse(row.permissions || '[]'),
        profile: JSON.parse(row.profile || '{}')
      })) as TenantUser[];
    } catch (error) {
      console.error('Error fetching tenant users:', error);
      return [];
    }
  }

  // Tenant Database Access
  async getTenantDatabase(tenantId: string) {
    return await this.dbManager.getTenantDatabase(tenantId);
  }

  getMasterDatabase() {
    return this.dbManager.getMasterDatabase();
  }

  // Utility Methods
  getAllTenants(): Tenant[] {
    return this.dbManager.getAllTenants();
  }

  getTenantStats() {
    return this.dbManager.getTenantStats();
  }

  async validateTenantAccess(tenantId: string, userId: string): Promise<boolean> {
    const user = this.getTenantUserById(userId);
    return user?.tenant_id === tenantId && user?.status === 'active';
  }

  private getTenantUserById(userId: string): TenantUser | null {
    const masterDb = this.dbManager.getMasterDatabase();
    
    try {
      const row = masterDb.prepare(`
        SELECT * FROM tenant_users WHERE id = ? AND status = 'active'
      `).get(userId);

      if (!row) return null;

      return {
        ...row,
        permissions: JSON.parse(row.permissions || '[]'),
        profile: JSON.parse(row.profile || '{}')
      } as TenantUser;
    } catch (error) {
      console.error('Error fetching tenant user by ID:', error);
      return null;
    }
  }

  // Subdomain validation
  isSubdomainAvailable(subdomain: string): boolean {
    if (!validateSubdomain(subdomain) || isSubdomainReserved(subdomain)) {
      return false;
    }

    const existingTenant = this.dbManager.getTenantBySubdomain(subdomain);
    return !existingTenant;
  }

  // Cleanup
  closeConnections(): void {
    this.dbManager.closeAllConnections();
  }
}
