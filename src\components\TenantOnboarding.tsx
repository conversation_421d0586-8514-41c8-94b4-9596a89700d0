import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { TenantOnboardingData, validateSubdomain, isSubdomainReserved } from '../types/tenant';
import tenantService from '../services/tenantService';

const TenantOnboarding: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<TenantOnboardingData>({
    name: '',
    subdomain: '',
    owner_email: '',
    owner_name: '',
    plan_type: 'basic'
  });

  const [subdomainStatus, setSubdomainStatus] = useState<{
    checking: boolean;
    available: boolean;
    message: string;
  }>({
    checking: false,
    available: false,
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (error) setError(null);
    
    // Check subdomain availability when subdomain changes
    if (name === 'subdomain') {
      checkSubdomainAvailability(value);
    }
  };

  const checkSubdomainAvailability = async (subdomain: string) => {
    if (!subdomain) {
      setSubdomainStatus({ checking: false, available: false, message: '' });
      return;
    }

    if (!validateSubdomain(subdomain)) {
      setSubdomainStatus({
        checking: false,
        available: false,
        message: 'Invalid subdomain format. Use only letters, numbers, and hyphens.'
      });
      return;
    }

    if (isSubdomainReserved(subdomain)) {
      setSubdomainStatus({
        checking: false,
        available: false,
        message: 'This subdomain is reserved and cannot be used.'
      });
      return;
    }

    setSubdomainStatus({ checking: true, available: false, message: 'Checking availability...' });

    try {
      const result = await tenantService.checkSubdomainAvailability(subdomain);
      setSubdomainStatus({
        checking: false,
        available: result.available,
        message: result.available ? 'Subdomain is available!' : result.reason || 'Subdomain is not available'
      });
    } catch (err) {
      setSubdomainStatus({
        checking: false,
        available: false,
        message: 'Error checking availability'
      });
    }
  };

  const handleNext = () => {
    if (step === 1) {
      // Validate step 1
      if (!formData.name || !formData.subdomain) {
        setError('Please fill in all required fields');
        return;
      }
      if (!subdomainStatus.available) {
        setError('Please choose an available subdomain');
        return;
      }
    }
    setStep(step + 1);
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.owner_email) {
      setError('Please provide an email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await tenantService.createTenant(formData);
      
      // Redirect to the new tenant subdomain
      const newUrl = tenantService.buildTenantUrl(formData.subdomain, '/login');
      window.location.href = newUrl;
    } catch (err: any) {
      setError(err.message || 'Failed to create store');
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900">
            Create Your Shoe Repair Store
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Set up your professional shoe repair business in minutes
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'}`}>
                1
              </div>
              <div className={`flex-1 h-1 mx-2 ${step >= 2 ? 'bg-blue-600' : 'bg-gray-300'}`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'}`}>
                2
              </div>
              <div className={`flex-1 h-1 mx-2 ${step >= 3 ? 'bg-blue-600' : 'bg-gray-300'}`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 3 ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'}`}>
                3
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-2">
              <span>Store Info</span>
              <span>Owner Details</span>
              <span>Complete</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="text-sm text-red-600">{error}</div>
              </div>
            )}

            {step === 1 && (
              <div className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Store Name *
                  </label>
                  <input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                    className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="e.g., Downtown Shoe Repair"
                  />
                </div>

                <div>
                  <label htmlFor="subdomain" className="block text-sm font-medium text-gray-700">
                    Choose Your URL *
                  </label>
                  <div className="mt-1 flex rounded-md shadow-sm">
                    <input
                      id="subdomain"
                      name="subdomain"
                      type="text"
                      required
                      value={formData.subdomain}
                      onChange={handleInputChange}
                      className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="yourstore"
                    />
                    <span className="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                      .shoerepair.com
                    </span>
                  </div>
                  {subdomainStatus.message && (
                    <p className={`mt-2 text-sm ${subdomainStatus.available ? 'text-green-600' : 'text-red-600'}`}>
                      {subdomainStatus.checking ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Checking...
                        </span>
                      ) : (
                        subdomainStatus.message
                      )}
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="plan_type" className="block text-sm font-medium text-gray-700">
                    Choose Your Plan
                  </label>
                  <select
                    id="plan_type"
                    name="plan_type"
                    value={formData.plan_type}
                    onChange={handleInputChange}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="trial">Trial (14 days free)</option>
                    <option value="basic">Basic ($29/month)</option>
                    <option value="professional">Professional ($79/month)</option>
                    <option value="enterprise">Enterprise ($199/month)</option>
                  </select>
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-6">
                <div>
                  <label htmlFor="owner_name" className="block text-sm font-medium text-gray-700">
                    Your Full Name
                  </label>
                  <input
                    id="owner_name"
                    name="owner_name"
                    type="text"
                    value={formData.owner_name}
                    onChange={handleInputChange}
                    className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="John Doe"
                  />
                </div>

                <div>
                  <label htmlFor="owner_email" className="block text-sm font-medium text-gray-700">
                    Email Address *
                  </label>
                  <input
                    id="owner_email"
                    name="owner_email"
                    type="email"
                    required
                    value={formData.owner_email}
                    onChange={handleInputChange}
                    className="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="<EMAIL>"
                  />
                  <p className="mt-2 text-sm text-gray-500">
                    This will be your login email and where we'll send important updates.
                  </p>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-6">
                <div className="text-center">
                  <div className="mx-auto h-12 w-12 text-green-400 mb-4">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Ready to Create Your Store!
                  </h3>
                  <div className="bg-gray-50 rounded-lg p-4 text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Summary:</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p><span className="font-medium">Store:</span> {formData.name}</p>
                      <p><span className="font-medium">URL:</span> {formData.subdomain}.shoerepair.com</p>
                      <p><span className="font-medium">Plan:</span> {formData.plan_type}</p>
                      <p><span className="font-medium">Owner:</span> {formData.owner_email}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between">
              {step > 1 && (
                <button
                  type="button"
                  onClick={handleBack}
                  className="flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Back
                </button>
              )}
              
              {step < 3 ? (
                <button
                  type="button"
                  onClick={handleNext}
                  className="ml-auto flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={loading}
                  className="ml-auto flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {loading ? 'Creating Store...' : 'Create Store'}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default TenantOnboarding;
