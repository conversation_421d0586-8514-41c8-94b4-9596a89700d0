import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import App from './App';
import './index.css';

// Auto-login as admin for direct dashboard access
const adminUser = {
  id: '1',
  email: '<EMAIL>',
  name: 'Admin User',
  role: 'admin',
  permissions: ['all'],
  active: true,
  lastLogin: '2024-03-15T08:30:00Z'
};

// Store user in localStorage for automatic login
localStorage.setItem('user', JSON.stringify(adminUser));

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </StrictMode>
);