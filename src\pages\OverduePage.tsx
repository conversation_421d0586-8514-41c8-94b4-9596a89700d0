import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  AlertCircle, 
  Search, 
  Filter, 
  Download, 
  Phone, 
  Calendar, 
  RefreshCw,
  Clock,
  Mail,
  MessageSquare
} from 'lucide-react';
import { Link } from 'react-router-dom';
import type { RepairItem } from '../types';
import { mockRepairs, pendingRepairs } from '../data/mockData';
import { format, parseISO, isBefore, addDays } from 'date-fns';

// In a real app, you would fetch this data from your API
const fetchOverdueItems = (): Promise<RepairItem[]> => {
  return new Promise((resolve) => {
    // Simulate API call delay
    setTimeout(() => {
      const today = new Date();
      
      // Combine all repairs that might be overdue
      const allRepairs = [...mockRepairs, ...pendingRepairs];
      
      // Filter for items where estimated completion date is before today
      const overdueItems = allRepairs.filter(item => {
        const completionDate = parseISO(item.estimatedCompletion);
        return isBefore(completionDate, today) && item.status !== 'completed' && item.status !== 'ready-for-pickup';
      });
      
      resolve(overdueItems);
    }, 800);
  });
};

interface OverdueStats {
  totalItems: number;
  oneToThreeDays: number;
  fourToSevenDays: number;
  overSevenDays: number;
}

const OverduePage: React.FC = () => {
  const [items, setItems] = useState<RepairItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<RepairItem[]>([]);
  const [stats, setStats] = useState<OverdueStats>({
    totalItems: 0,
    oneToThreeDays: 0,
    fourToSevenDays: 0,
    overSevenDays: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [daysOverdueFilter, setDaysOverdueFilter] = useState<string>('all');
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await fetchOverdueItems();
        setItems(data);
        
        // Calculate stats
        const today = new Date();
        let oneToThree = 0;
        let fourToSeven = 0;
        let overSeven = 0;
        
        data.forEach(item => {
          const completionDate = parseISO(item.estimatedCompletion);
          const daysOverdue = Math.floor((today.getTime() - completionDate.getTime()) / (1000 * 60 * 60 * 24));
          
          if (daysOverdue <= 3) {
            oneToThree++;
          } else if (daysOverdue <= 7) {
            fourToSeven++;
          } else {
            overSeven++;
          }
        });
        
        setStats({
          totalItems: data.length,
          oneToThreeDays: oneToThree,
          fourToSevenDays: fourToSeven,
          overSevenDays: overSeven
        });
        
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [refreshKey]);

  // Filter items based on search query and days overdue filter
  useEffect(() => {
    let result = items;
    const today = new Date();
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item => 
        item.customerName.toLowerCase().includes(query) || 
        item.description.toLowerCase().includes(query) ||
        item.contactNumber.includes(query)
      );
    }
    
    // Apply days overdue filter
    if (daysOverdueFilter !== 'all') {
      result = result.filter(item => {
        const completionDate = parseISO(item.estimatedCompletion);
        const daysOverdue = Math.floor((today.getTime() - completionDate.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysOverdueFilter === '1-3') {
          return daysOverdue >= 1 && daysOverdue <= 3;
        } else if (daysOverdueFilter === '4-7') {
          return daysOverdue >= 4 && daysOverdue <= 7;
        } else if (daysOverdueFilter === '7+') {
          return daysOverdue > 7;
        }
        return true;
      });
    }
    
    setFilteredItems(result);
  }, [items, searchQuery, daysOverdueFilter]);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleExport = () => {
    // In a real app, you would implement CSV export functionality
    alert('Export functionality would be implemented here');
  };

  const getDaysOverdue = (estimatedCompletion: string) => {
    const completionDate = parseISO(estimatedCompletion);
    const today = new Date();
    return Math.floor((today.getTime() - completionDate.getTime()) / (1000 * 60 * 60 * 24));
  };

  const getOverdueSeverity = (daysOverdue: number) => {
    if (daysOverdue <= 3) {
      return 'bg-yellow-100 text-yellow-800';
    } else if (daysOverdue <= 7) {
      return 'bg-orange-100 text-orange-800';
    } else {
      return 'bg-red-100 text-red-800';
    }
  };

  const handleContactCustomer = (item: RepairItem, method: 'sms' | 'email' | 'call') => {
    // In a real app, you would implement contact functionality
    alert(`Contact ${item.customerName} via ${method}`);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <Link to="/" className="mr-4 text-gray-500 hover:text-gray-700">
            <ArrowLeft size={24} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Overdue Items</h1>
            <p className="text-gray-500">Items past their estimated completion date</p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={handleRefresh}
            className="flex items-center justify-center px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <RefreshCw size={18} className="mr-2" />
            Refresh
          </button>
          
          <button
            onClick={handleExport}
            className="flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            <Download size={18} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 border-l-4 border-red-500 text-red-700">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {/* Stats Cards */}
      {loading ? (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-red-500">
            <p className="text-gray-500 text-sm mb-1">Total Overdue</p>
            <p className="text-2xl font-bold text-red-600">{stats.totalItems}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-yellow-500">
            <p className="text-gray-500 text-sm mb-1">1-3 Days</p>
            <p className="text-2xl font-bold text-yellow-600">{stats.oneToThreeDays}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-orange-500">
            <p className="text-gray-500 text-sm mb-1">4-7 Days</p>
            <p className="text-2xl font-bold text-orange-600">{stats.fourToSevenDays}</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg shadow border-l-4 border-red-700">
            <p className="text-gray-500 text-sm mb-1">Over 7 Days</p>
            <p className="text-2xl font-bold text-red-700">{stats.overSevenDays}</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by customer name or phone..."
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="w-full md:w-64">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={18} className="text-gray-400" />
            </div>
            <select
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 appearance-none"
              value={daysOverdueFilter}
              onChange={(e) => setDaysOverdueFilter(e.target.value)}
            >
              <option value="all">All Overdue</option>
              <option value="1-3">1-3 Days</option>
              <option value="4-7">4-7 Days</option>
              <option value="7+">Over 7 Days</option>
            </select>
          </div>
        </div>
      </div>

      {/* Items List */}
      {loading ? (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : filteredItems.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <AlertCircle size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-medium text-gray-900 mb-2">No overdue items found</h3>
          <p className="text-gray-500 mb-4">
            There are no items past their estimated completion date, or your search filters don't match any items.
          </p>
          <button
            onClick={() => {
              setSearchQuery('');
              setDaysOverdueFilter('all');
            }}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Clear Filters
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Item
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Days Overdue
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.map((item) => {
                  const daysOverdue = getDaysOverdue(item.estimatedCompletion);
                  return (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{item.customerName}</div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Phone size={14} className="mr-1" />
                          {item.contactNumber}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 capitalize">{item.type}</div>
                        <div className="text-sm text-gray-500">{item.description}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {item.status === 'pending' ? (
                            <AlertCircle size={14} className="mr-1" />
                          ) : (
                            <Clock size={14} className="mr-1" />
                          )}
                          {item.status.replace('-', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-500">
                          <Calendar size={14} className="mr-1" />
                          {format(parseISO(item.estimatedCompletion), 'MMM dd, yyyy')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getOverdueSeverity(daysOverdue)}`}>
                          <AlertCircle size={14} className="mr-1" />
                          {daysOverdue} {daysOverdue === 1 ? 'day' : 'days'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            className="text-indigo-600 hover:text-indigo-900"
                            onClick={() => alert(`View details for ${item.customerName}`)}
                          >
                            View
                          </button>
                          <div className="border-r border-gray-300"></div>
                          <button
                            className="text-blue-600 hover:text-blue-900"
                            onClick={() => handleContactCustomer(item, 'sms')}
                            title="Send SMS"
                          >
                            <MessageSquare size={16} />
                          </button>
                          <button
                            className="text-red-600 hover:text-red-900"
                            onClick={() => handleContactCustomer(item, 'email')}
                            title="Send Email"
                          >
                            <Mail size={16} />
                          </button>
                          <button
                            className="text-green-600 hover:text-green-900"
                            onClick={() => handleContactCustomer(item, 'call')}
                            title="Call Customer"
                          >
                            <Phone size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default OverduePage;
