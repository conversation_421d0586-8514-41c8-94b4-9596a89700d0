import React, { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogT<PERSON>le, 
  DialogContent, 
  DialogActions, 
  TextField, 
  Button, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  FormControlLabel, 
  Switch, 
  Typography
} from '@mui/material';
import { DateTimePicker } from '@mui/lab';
import { HoldFormData, HoldItem, HoldStatus } from '../types/holdTypes';

interface HoldItemFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: HoldFormData) => void;
  initialData?: Partial<HoldItem>;
  title: string;
}

const defaultFormData: HoldFormData = {
  customer_name: '',
  customer_phone: '',
  customer_email: '',
  item_description: '',
  expected_completion: new Date().toISOString(),
  notes: '',
  status: 'pending',
  is_quick_drop: false
};

const HoldItemForm: React.FC<HoldItemFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialData,
  title
}) => {
  const [formData, setFormData] = useState<HoldFormData>({
    ...defaultFormData,
    ...initialData,
    is_quick_drop: initialData?.is_quick_drop === 1
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleDateChange = (date: Date | null) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        expected_completion: date.toISOString()
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle className="bg-blue-50 text-blue-800">
        {title}
      </DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent className="space-y-4 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextField
              name="customer_name"
              label="Customer Name"
              value={formData.customer_name}
              onChange={handleChange}
              fullWidth
              required
              variant="outlined"
            />
            <TextField
              name="customer_phone"
              label="Customer Phone"
              value={formData.customer_phone || ''}
              onChange={handleChange}
              fullWidth
              variant="outlined"
            />
            <TextField
              name="customer_email"
              label="Customer Email"
              value={formData.customer_email || ''}
              onChange={handleChange}
              fullWidth
              variant="outlined"
              type="email"
            />
            <FormControl fullWidth>
              <InputLabel id="status-label">Status</InputLabel>
              <Select
                labelId="status-label"
                name="status"
                value={formData.status}
                onChange={handleChange}
                label="Status"
              >
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="in-progress">In Progress</MenuItem>
                <MenuItem value="ready">Ready</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
              </Select>
            </FormControl>
          </div>

          <TextField
            name="item_description"
            label="Item Description"
            value={formData.item_description}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            multiline
            rows={2}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DateTimePicker
              label="Expected Completion"
              value={new Date(formData.expected_completion)}
              onChange={handleDateChange}
              renderInput={(params) => <TextField {...params} fullWidth />}
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_quick_drop}
                  onChange={handleSwitchChange}
                  name="is_quick_drop"
                  color="primary"
                />
              }
              label={
                <Typography variant="body1">
                  Quick Drop (Same-day service)
                </Typography>
              }
            />
          </div>

          <TextField
            name="notes"
            label="Notes"
            value={formData.notes || ''}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions className="bg-gray-50 p-4">
          <Button onClick={onClose} variant="outlined" color="inherit">
            Cancel
          </Button>
          <Button type="submit" variant="contained" color="primary">
            Save
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default HoldItemForm;
