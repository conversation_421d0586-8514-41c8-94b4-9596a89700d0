version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspaces:cached
    command: sleep infinity
    network_mode: service:db
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:3001
    depends_on:
      - db

  db:
    image: alpine:latest
    command: sleep infinity
    ports:
      - "3001:3001"
      - "5174:5174"
      - "5173:5173"
    volumes:
      - db-data:/data

volumes:
  db-data:
