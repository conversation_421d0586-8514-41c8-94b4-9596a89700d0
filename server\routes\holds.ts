import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import db from '../database';

const router = express.Router();

// Initialize the hold_items table if it doesn't exist
try {
  const holdSchemaSQL = `
    -- Hold items and quick drops tables
    CREATE TABLE IF NOT EXISTS hold_items (
        id TEXT PRIMARY KEY,
        customer_name TEXT NOT NULL,
        customer_phone TEXT,
        customer_email TEXT,
        item_description TEXT NOT NULL,
        hold_date TEXT DEFAULT CURRENT_TIMESTAMP,
        expected_completion TEXT,
        notes TEXT,
        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'in-progress', 'ready', 'completed')),
        is_quick_drop BOOLEAN DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `;
  db.exec(holdSchemaSQL);
} catch (error) {
  console.error('Error initializing hold_items table:', error);
}

// Get all hold items
router.get('/', (req, res) => {
  try {
    const holdItems = db.prepare(`
      SELECT * FROM hold_items
      ORDER BY hold_date DESC
    `).all();
    
    res.json(holdItems);
  } catch (error) {
    console.error('Error fetching hold items:', error);
    res.status(500).json({ error: 'Failed to fetch hold items' });
  }
});

// Get hold item by ID
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const holdItem = db.prepare(`
      SELECT * FROM hold_items
      WHERE id = ?
    `).get(id);
    
    if (!holdItem) {
      return res.status(404).json({ error: 'Hold item not found' });
    }
    
    res.json(holdItem);
  } catch (error) {
    console.error('Error fetching hold item:', error);
    res.status(500).json({ error: 'Failed to fetch hold item' });
  }
});

// Create new hold item
router.post('/', (req, res) => {
  try {
    const {
      customer_name,
      customer_phone,
      customer_email,
      item_description,
      expected_completion,
      notes,
      status = 'pending',
      is_quick_drop = false
    } = req.body;
    
    if (!customer_name || !item_description) {
      return res.status(400).json({ error: 'Customer name and item description are required' });
    }
    
    const id = uuidv4();
    const now = new Date().toISOString();
    
    db.prepare(`
      INSERT INTO hold_items (
        id, 
        customer_name, 
        customer_phone, 
        customer_email, 
        item_description, 
        hold_date, 
        expected_completion, 
        notes, 
        status, 
        is_quick_drop, 
        created_at, 
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id,
      customer_name,
      customer_phone || null,
      customer_email || null,
      item_description,
      now,
      expected_completion || null,
      notes || null,
      status,
      is_quick_drop ? 1 : 0,
      now,
      now
    );
    
    const newHoldItem = db.prepare('SELECT * FROM hold_items WHERE id = ?').get(id);
    res.status(201).json(newHoldItem);
  } catch (error) {
    console.error('Error creating hold item:', error);
    res.status(500).json({ error: 'Failed to create hold item' });
  }
});

// Update hold item
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const {
      customer_name,
      customer_phone,
      customer_email,
      item_description,
      expected_completion,
      notes,
      status,
      is_quick_drop
    } = req.body;
    
    const holdItem = db.prepare('SELECT * FROM hold_items WHERE id = ?').get(id);
    if (!holdItem) {
      return res.status(404).json({ error: 'Hold item not found' });
    }
    
    const now = new Date().toISOString();
    
    db.prepare(`
      UPDATE hold_items SET
        customer_name = ?,
        customer_phone = ?,
        customer_email = ?,
        item_description = ?,
        expected_completion = ?,
        notes = ?,
        status = ?,
        is_quick_drop = ?,
        updated_at = ?
      WHERE id = ?
    `).run(
      customer_name || holdItem.customer_name,
      customer_phone !== undefined ? customer_phone : holdItem.customer_phone,
      customer_email !== undefined ? customer_email : holdItem.customer_email,
      item_description || holdItem.item_description,
      expected_completion !== undefined ? expected_completion : holdItem.expected_completion,
      notes !== undefined ? notes : holdItem.notes,
      status || holdItem.status,
      is_quick_drop !== undefined ? (is_quick_drop ? 1 : 0) : holdItem.is_quick_drop,
      now,
      id
    );
    
    const updatedHoldItem = db.prepare('SELECT * FROM hold_items WHERE id = ?').get(id);
    res.json(updatedHoldItem);
  } catch (error) {
    console.error('Error updating hold item:', error);
    res.status(500).json({ error: 'Failed to update hold item' });
  }
});

// Delete hold item
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    
    const holdItem = db.prepare('SELECT * FROM hold_items WHERE id = ?').get(id);
    if (!holdItem) {
      return res.status(404).json({ error: 'Hold item not found' });
    }
    
    db.prepare('DELETE FROM hold_items WHERE id = ?').run(id);
    
    res.json({ message: 'Hold item deleted successfully' });
  } catch (error) {
    console.error('Error deleting hold item:', error);
    res.status(500).json({ error: 'Failed to delete hold item' });
  }
});

// Get stats for hold items and quick drops
router.get('/stats/summary', (req, res) => {
  try {
    const totalHoldItems = db.prepare(`
      SELECT COUNT(*) as count FROM hold_items WHERE is_quick_drop = 0
    `).get().count;
    
    const totalQuickDrops = db.prepare(`
      SELECT COUNT(*) as count FROM hold_items WHERE is_quick_drop = 1
    `).get().count;
    
    const today = new Date().toISOString().split('T')[0];
    const dueToday = db.prepare(`
      SELECT COUNT(*) as count FROM hold_items 
      WHERE date(expected_completion) = ?
    `).get(today).count;
    
    const pendingItems = db.prepare(`
      SELECT COUNT(*) as count FROM hold_items 
      WHERE status = 'pending'
    `).get().count;
    
    const inProgressItems = db.prepare(`
      SELECT COUNT(*) as count FROM hold_items 
      WHERE status = 'in-progress'
    `).get().count;
    
    const readyItems = db.prepare(`
      SELECT COUNT(*) as count FROM hold_items 
      WHERE status = 'ready'
    `).get().count;
    
    res.json({
      totalHoldItems,
      totalQuickDrops,
      dueToday,
      pendingItems,
      inProgressItems,
      readyItems
    });
  } catch (error) {
    console.error('Error fetching hold stats:', error);
    res.status(500).json({ error: 'Failed to fetch hold stats' });
  }
});

// Get today's timeline (items due today)
router.get('/timeline/today', (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    const timelineItems = db.prepare(`
      SELECT * FROM hold_items 
      WHERE date(expected_completion) = ?
      ORDER BY expected_completion ASC
    `).all(today);
    
    res.json(timelineItems);
  } catch (error) {
    console.error('Error fetching timeline:', error);
    res.status(500).json({ error: 'Failed to fetch timeline' });
  }
});

export default router;
