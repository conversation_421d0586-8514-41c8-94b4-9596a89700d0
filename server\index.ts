import express from 'express';
import cors from 'cors';
import { v4 as uuidv4 } from 'uuid';
import db, { DatabaseManager } from './database';
import operationsRouter from './operations';
import inventoryRouter from './routes/inventory';
import printerRouter from './routes/printer';
import salesRoutes from './routes/sales';
import qrCodesRouter from './routes/qrcodes';
import suppliesRouter from './routes/supplies';
import categoriesRouter from './routes/categories';
import categoryRoutes from './routes/categoryRoutes';
import holdsRouter from './routes/holds';
import tenantsRouter from './routes/tenants';
import customersRouter from './routes/customers';
import { tenantMiddleware } from './middleware/tenantMiddleware';
import { transformCustomer, transformOperation, transformService } from './utils';

const app = express();
const port = 3001;

app.use(cors());
app.use(express.json());

// Add error handling middleware
app.use((err: any, req: any, res: any, next: any) => {
  console.error('Error:', err);
  res.status(500).json({ error: err.message });
});

// Enhanced health check endpoint
app.get('/api/health', (req, res) => {
  try {
    const dbManager = DatabaseManager.getInstance();
    const isHealthy = dbManager.healthCheck();
    const stats = dbManager.getStats();
    const tenantStats = dbManager.getTenantDatabaseManager().getTenantStats();

    res.json({
      status: isHealthy ? 'ok' : 'error',
      time: new Date().toISOString(),
      database: {
        healthy: isHealthy,
        stats: stats
      },
      multiTenant: {
        enabled: process.env.ENABLE_MULTI_TENANCY === 'true',
        stats: tenantStats
      },
      version: process.env.APP_VERSION || '1.0.0'
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      status: 'error',
      time: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
});

// Add tenant middleware for debugging headers
app.use(tenantMiddleware.addTenantHeaders);

// Tenant management routes (no tenant context required for some routes)
app.use('/api/tenants', tenantsRouter);

// Apply tenant middleware to business routes
const tenantAwareRoutes = express.Router();
tenantAwareRoutes.use(tenantMiddleware.resolveTenant);
tenantAwareRoutes.use(tenantMiddleware.resolveTenantUser);
tenantAwareRoutes.use(tenantMiddleware.trackUsage);

// Use routers with tenant context
tenantAwareRoutes.use('/customers', customersRouter);
tenantAwareRoutes.use('/operations', operationsRouter);
tenantAwareRoutes.use('/inventory', inventoryRouter);
tenantAwareRoutes.use('/printer', printerRouter);
tenantAwareRoutes.use('/sales', salesRoutes);
tenantAwareRoutes.use('/qrcodes', qrCodesRouter);
tenantAwareRoutes.use('/supplies', suppliesRouter);
tenantAwareRoutes.use('/', categoryRoutes);
tenantAwareRoutes.use('/holds', holdsRouter);

// Mount tenant-aware routes
app.use('/api', tenantAwareRoutes);

// Customer endpoints are now handled by tenant-aware routes

// Order routes are now handled by tenant-aware routes

// All business endpoints are now handled by tenant-aware routes

// Start server
app.listen(port, () => {
  console.log(`🚀 Server is running on http://localhost:${port}`);

  // Test database connection and show stats
  try {
    const dbManager = DatabaseManager.getInstance();
    const isHealthy = dbManager.healthCheck();
    const stats = dbManager.getStats();

    if (isHealthy) {
      console.log('✅ Database connection healthy');
      console.log('📊 Database statistics:', stats);
    } else {
      console.error('❌ Database health check failed');
    }
  } catch (error) {
    console.error('❌ Database connection error:', error);
  }
});

// Handle process errors
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
});
